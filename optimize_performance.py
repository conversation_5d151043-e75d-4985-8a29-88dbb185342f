#!/usr/bin/env python3
"""
性能优化脚本 - 调整模型参数以提升推理速度
"""

import os
import json

def optimize_config():
    """优化配置以提升性能"""
    
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    # 读取当前配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 当前配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 优化配置
    optimized_config = config.copy()
    
    # 减少上下文长度以节省内存和提升速度
    optimized_config["context_length"] = 2048  # 从 32768 减少到 2048
    
    # 减少最大生成 token 数
    optimized_config["max_tokens"] = 512  # 从 2048 减少到 512
    
    # 增加线程数（利用所有 CPU 核心）
    import multiprocessing
    optimized_config["threads"] = multiprocessing.cpu_count()
    
    # 调整其他参数
    optimized_config["temperature"] = 0.3  # 降低随机性，提升速度
    optimized_config["top_p"] = 0.9
    optimized_config["repeat_penalty"] = 1.05
    
    print("\n⚡ 优化后配置:")
    for key, value in optimized_config.items():
        if value != config.get(key):
            print(f"  {key}: {config.get(key)} → {value} ✨")
        else:
            print(f"  {key}: {value}")
    
    # 保存优化配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 配置已优化并保存到: {config_path}")
    return True

def show_performance_tips():
    """显示性能优化建议"""
    print("\n💡 性能优化建议:")
    print("=" * 50)
    
    print("1. 🔧 模型参数优化:")
    print("   - 减少 context_length (2048 vs 32768)")
    print("   - 减少 max_tokens (512 vs 2048)")
    print("   - 降低 temperature (0.3 vs 0.7)")
    
    print("\n2. 🚀 系统优化:")
    print("   - 关闭其他占用 CPU 的应用")
    print("   - 确保有足够的可用内存")
    print("   - 使用 SSD 存储模型文件")
    
    print("\n3. 📊 预期性能 (Mac M2 Pro):")
    print("   - 优化前: 10-15秒/响应")
    print("   - 优化后: 3-8秒/响应")
    print("   - 短文本: 2-4秒")
    print("   - 长文本: 5-8秒")
    
    print("\n4. 🎯 使用建议:")
    print("   - 保持提问简洁明了")
    print("   - 避免过长的上下文")
    print("   - 批量处理相似任务")

if __name__ == "__main__":
    print("⚡ Qwen LLM Platform 性能优化")
    print("=" * 60)
    
    if optimize_config():
        show_performance_tips()
        print("\n🚀 重启服务以应用优化:")
        print("   1. 停止当前服务 (Ctrl+C)")
        print("   2. 运行: ./start_from_root.py")
    else:
        print("❌ 优化失败")
