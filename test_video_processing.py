#!/usr/bin/env python3
"""
测试视频处理功能
"""

import requests
import os
import time

def check_service():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务运行正常")
            return True
        else:
            print(f"❌ 服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        return False

def check_video_capabilities():
    """检查视频处理能力"""
    print("\n📋 检查视频处理能力...")
    
    try:
        response = requests.get("http://localhost:8000/video_processing_info", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ 视频处理信息:")
            print(f"   支持格式: {', '.join(data.get('supported_formats', []))}")
            print(f"   最大文件: {data.get('max_file_size_mb', 0)}MB")
            
            deps = data.get('dependencies', {})
            print("   依赖状态:")
            for dep, available in deps.items():
                status = "✅" if available else "❌"
                print(f"     {dep}: {status}")
            
            methods = data.get('extract_methods', [])
            print(f"   提取方法: {', '.join(methods) if methods else '无'}")
            
            languages = data.get('target_languages', {})
            print(f"   支持语言: {len(languages)} 种")
            
            return all(deps.values()) if deps else False
            
        else:
            print(f"❌ 获取信息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def create_test_video():
    """创建测试视频（如果有ffmpeg）"""
    print("\n🎬 创建测试视频...")
    
    try:
        import subprocess
        
        # 创建一个简单的测试视频（5秒，带音频）
        cmd = [
            'ffmpeg', '-f', 'lavfi', '-i', 'testsrc2=duration=5:size=320x240:rate=30',
            '-f', 'lavfi', '-i', 'sine=frequency=1000:duration=5',
            '-c:v', 'libx264', '-c:a', 'aac', '-shortest',
            '-y', 'test_video.mp4'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and os.path.exists('test_video.mp4'):
            print("✅ 测试视频创建成功: test_video.mp4")
            return 'test_video.mp4'
        else:
            print("❌ 测试视频创建失败")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试视频失败: {e}")
        return None

def test_video_upload(video_file):
    """测试视频上传和处理"""
    print(f"\n📤 测试视频上传: {video_file}")
    
    if not os.path.exists(video_file):
        print(f"❌ 视频文件不存在: {video_file}")
        return False
    
    file_size = os.path.getsize(video_file)
    print(f"   文件大小: {file_size / (1024*1024):.2f}MB")
    
    try:
        with open(video_file, 'rb') as f:
            files = {'file': (video_file, f, 'video/mp4')}
            data = {
                'target_language': 'zh',
                'extract_method': 'whisper'
            }
            
            print("🔄 上传并处理视频...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:8000/process_video",
                files=files,
                data=data,
                timeout=300  # 5分钟超时
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ 视频处理成功 (耗时: {elapsed:.1f}s)")
                print(f"   视频文件: {result.get('video_file', '')}")
                print(f"   目标语言: {result.get('target_language', '')}")
                print(f"   检测语言: {result.get('detected_language', '未知')}")
                print(f"   处理步骤: {', '.join(result.get('processing_steps', []))}")
                
                # 显示字幕
                original = result.get('original_subtitles', '')
                translated = result.get('translated_subtitles', '')
                
                if original:
                    print("\n📝 原始字幕:")
                    print("-" * 40)
                    print(original[:500] + "..." if len(original) > 500 else original)
                    print("-" * 40)
                
                if translated:
                    print("\n🌐 翻译字幕:")
                    print("-" * 40)
                    print(translated[:500] + "..." if len(translated) > 500 else translated)
                    print("-" * 40)
                
                # 保存字幕文件
                if translated:
                    subtitle_file = f"{os.path.splitext(video_file)[0]}_translated.srt"
                    with open(subtitle_file, 'w', encoding='utf-8') as f:
                        f.write(translated)
                    print(f"✅ 翻译字幕已保存: {subtitle_file}")
                
                return True
                
            else:
                print(f"❌ 视频处理失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 视频上传异常: {e}")
        return False

def test_with_sample_files():
    """使用示例文件测试"""
    print("\n📁 查找示例视频文件...")
    
    # 常见的示例视频文件位置
    sample_locations = [
        "sample.mp4",
        "test.mp4",
        "demo.mp4",
        "example.mp4",
        os.path.expanduser("~/Desktop/sample.mp4"),
        os.path.expanduser("~/Downloads/sample.mp4"),
    ]
    
    for location in sample_locations:
        if os.path.exists(location):
            print(f"✅ 找到示例文件: {location}")
            return test_video_upload(location)
    
    print("❌ 未找到示例视频文件")
    print("\n💡 建议:")
    print("1. 将一个小的MP4文件重命名为 sample.mp4 放在当前目录")
    print("2. 或者使用 create_test_video() 创建测试视频")
    return False

def main():
    """主测试流程"""
    print("🎥 视频处理功能测试")
    print("=" * 60)
    
    # 1. 检查服务
    if not check_service():
        print("\n❌ 服务不可用，请先启动服务:")
        print("   python fast_start.py")
        return
    
    # 2. 检查视频处理能力
    capabilities_ok = check_video_capabilities()
    
    if not capabilities_ok:
        print("\n⚠️  视频处理依赖不完整")
        print("请运行: python install_video_deps.py")
        
        # 仍然可以尝试测试
        choice = input("\n是否继续测试? (y/N): ")
        if choice.lower() != 'y':
            return
    
    # 3. 测试视频处理
    print("\n" + "=" * 60)
    print("开始视频处理测试...")
    
    # 尝试创建测试视频
    test_video = create_test_video()
    
    if test_video:
        success = test_video_upload(test_video)
    else:
        # 使用现有文件测试
        success = test_with_sample_files()
    
    # 4. 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 视频处理功能测试成功！")
        print("\n💡 使用方法:")
        print("1. 准备视频文件 (MP4, AVI, MOV等)")
        print("2. 使用 /process_video 端点上传")
        print("3. 指定目标语言进行翻译")
        print("4. 获得翻译后的字幕文件")
    else:
        print("❌ 视频处理功能测试失败")
        print("\n🔧 故障排除:")
        print("1. 检查依赖: python install_video_deps.py")
        print("2. 确保服务正常运行")
        print("3. 检查视频文件格式和大小")

if __name__ == "__main__":
    main()
