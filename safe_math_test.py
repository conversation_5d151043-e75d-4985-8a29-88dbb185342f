#!/usr/bin/env python3
"""
安全的数学试卷测试
"""

import requests
import json
import time

def test_service_health():
    """测试服务健康状态"""
    print("🔍 检查服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务正常运行")
            print(f"   模型加载: {data.get('model_loaded', 'unknown')}")
            return True
        else:
            print(f"❌ 服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务连接失败: {e}")
        return False

def test_simple_request():
    """测试简单请求"""
    print("\n🧪 测试简单请求...")
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": "hello",
                "max_tokens": 20
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 简单请求成功")
            print(f"   响应: {data.get('response', '')}")
            return True
        else:
            print(f"❌ 简单请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 简单请求异常: {e}")
        return False

def test_safe_math_paper():
    """测试安全的数学试卷"""
    print("\n📝 测试安全数学试卷...")
    
    # 使用简化的数学符号，避免复杂LaTeX
    safe_math_content = """请批改以下高中数学试卷：

一、选择题（每题5分）

1. 已知log₂a + log₂b = 1，则3^a + 9^b的最小值为（  ）
   A. 6    B. 6√3    C. 18    D. 24√3

2. 直线2x-3y+1=0与直线3x+2y+4=0垂直，则直线l的方程是（  ）
   A. 3x+2y-1=0    B. 3x+2y+7=0    C. 2x-3y+5=0    D. 2x-3y+8=0

3. 函数y=Asin(ωx+φ)中，A>0，ω>0，|φ|<π/2，求函数解析式

二、填空题（每题4分）

1. 已知集合A={x|x²-3x+2=0}，B={x|x²-ax+a-1=0}，若A∪B=A，则a=______

2. 若tanα=2，则(sinα+cosα)/(sinα-cosα)的值为______

三、解答题（每题10分）

1. 计算：log₂3·log₃4·log₄5·log₅6·log₆7·log₇8

2. 已知复数z=(2+i)/(1-i)，求|z|和z的共轭复数

请给出详细的解答过程。"""
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": safe_math_content,
                "max_tokens": 1200,
                "temperature": 0.6
            },
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            tokens_used = data.get('tokens_used', 0)
            processing_time = data.get('processing_time', 0)
            
            print("✅ 数学试卷批改成功")
            print(f"   Tokens: {tokens_used}/1200")
            print(f"   时间: {processing_time:.1f}s")
            print(f"   长度: {len(response_text)} 字符")
            
            print("\n📋 批改结果:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)
            
            return True
            
        else:
            print(f"❌ 数学试卷批改失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 数学试卷批改异常: {e}")
        return False

def test_individual_problems():
    """逐个测试数学问题"""
    print("\n🔢 逐个测试数学问题...")
    
    problems = [
        {
            "title": "对数计算",
            "content": "计算：log₂3·log₃4·log₄5·log₅6·log₆7·log₇8",
            "max_tokens": 300
        },
        {
            "title": "复数运算",
            "content": "已知复数z=(2+i)/(1-i)，求|z|和z的共轭复数",
            "max_tokens": 250
        },
        {
            "title": "三角函数",
            "content": "若tanα=2，则(sinα+cosα)/(sinα-cosα)的值为多少？",
            "max_tokens": 200
        }
    ]
    
    success_count = 0
    
    for i, problem in enumerate(problems, 1):
        print(f"\n问题 {i}: {problem['title']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json={
                    "message": f"请详细解答这个数学问题：{problem['content']}",
                    "max_tokens": problem['max_tokens'],
                    "temperature": 0.5
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)
                
                print(f"✅ 解答成功 ({tokens_used} tokens)")
                print(f"解答: {response_text}")
                success_count += 1
                
            else:
                print(f"❌ 解答失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 解答异常: {e}")
        
        time.sleep(2)  # 间隔2秒
    
    print(f"\n📊 个别问题测试结果: {success_count}/{len(problems)} 成功")
    return success_count == len(problems)

def main():
    """主函数"""
    print("🔧 安全数学试卷测试")
    print("=" * 60)
    
    # 1. 检查服务健康状态
    if not test_service_health():
        print("\n❌ 服务不可用，请重启服务:")
        print("   python fast_start.py")
        return
    
    # 2. 测试简单请求
    if not test_simple_request():
        print("\n❌ 基础功能异常，建议重启服务")
        return
    
    # 3. 测试安全的数学试卷
    math_success = test_safe_math_paper()
    
    # 4. 如果整体测试失败，尝试逐个问题
    if not math_success:
        print("\n🔄 整体测试失败，尝试逐个问题...")
        test_individual_problems()
    
    print("\n💡 使用建议:")
    print("1. 避免使用复杂的LaTeX公式")
    print("2. 使用简化的数学符号（如x²而不是x^2）")
    print("3. 分题目逐个批改获得更好效果")
    print("4. 如果服务异常，重启后再试")

if __name__ == "__main__":
    main()
