#!/usr/bin/env python3
"""
极致性能优化 - 最大化推理速度
"""

import os
import json
import multiprocessing

def extreme_optimize():
    """极致优化配置"""
    
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    # 读取当前配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 当前配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 极致优化配置
    optimized_config = config.copy()
    
    # 大幅减少上下文长度
    optimized_config["context_length"] = 1024  # 进一步减少到 1024
    
    # 减少最大生成 token 数
    optimized_config["max_tokens"] = 256  # 减少到 256
    
    # 最大化线程数
    cpu_count = multiprocessing.cpu_count()
    optimized_config["threads"] = cpu_count
    
    # 优化推理参数
    optimized_config["temperature"] = 0.1  # 降低随机性
    optimized_config["top_p"] = 0.8
    optimized_config["repeat_penalty"] = 1.02
    
    # 添加新的优化参数
    optimized_config["n_batch"] = 512  # 批处理大小
    optimized_config["n_predict"] = 128  # 预测长度
    
    print("\n⚡ 极致优化后配置:")
    for key, value in optimized_config.items():
        if value != config.get(key):
            print(f"  {key}: {config.get(key, 'new')} → {value} ✨")
        else:
            print(f"  {key}: {value}")
    
    # 保存优化配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 极致优化配置已保存")
    return True

def create_fast_model_wrapper():
    """创建快速模型包装器"""
    
    wrapper_code = '''
import os
import time
from typing import Optional, Dict, Any
from llama_cpp import Llama

class FastQwenModel:
    """快速 Qwen 模型包装器"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        
    def load_model(self, model_path: str):
        """加载模型（优化参数）"""
        print(f"🚀 快速加载模型: {model_path}")
        
        self.model = Llama(
            model_path=model_path,
            n_ctx=1024,          # 小上下文
            n_threads=os.cpu_count(),  # 最大线程
            n_batch=512,         # 批处理
            verbose=False,       # 关闭详细输出
            use_mmap=True,       # 使用内存映射
            use_mlock=True,      # 锁定内存
            n_gpu_layers=0,      # CPU 推理
        )
        
        self.is_loaded = True
        print("✅ 快速模型加载完成")
        
    def generate_fast(self, prompt: str, max_tokens: int = 128) -> str:
        """快速生成"""
        if not self.is_loaded:
            return "模型未加载"
            
        start_time = time.time()
        
        result = self.model(
            prompt,
            max_tokens=max_tokens,
            temperature=0.1,     # 低随机性
            top_p=0.8,
            repeat_penalty=1.02,
            stop=["\\n\\n", "<|endoftext|>", "<|im_end|>"],  # 早停
            echo=False           # 不回显输入
        )
        
        elapsed = time.time() - start_time
        response = result["choices"][0]["text"].strip()
        
        print(f"⚡ 生成耗时: {elapsed:.2f}s, Token数: {result['usage']['total_tokens']}")
        
        return response
'''
    
    with open("fast_model.py", "w", encoding="utf-8") as f:
        f.write(wrapper_code)
    
    print("✅ 快速模型包装器已创建: fast_model.py")

def optimize_system_settings():
    """优化系统设置建议"""
    print("\n🔧 系统优化建议:")
    print("=" * 50)
    
    print("1. 🚀 内存优化:")
    print("   - 关闭不必要的应用程序")
    print("   - 清理内存缓存")
    print("   - 确保至少 8GB 可用内存")
    
    print("\n2. ⚡ CPU 优化:")
    print("   - 关闭后台进程")
    print("   - 设置高性能模式")
    print("   - 确保 CPU 不过热")
    
    print("\n3. 💾 存储优化:")
    print("   - 使用 SSD 存储模型")
    print("   - 确保足够的可用空间")
    print("   - 定期清理临时文件")
    
    print("\n4. 🎯 使用技巧:")
    print("   - 使用简短明确的提示")
    print("   - 避免复杂的多轮对话")
    print("   - 批量处理相似请求")

def create_speed_test():
    """创建速度测试脚本"""
    
    test_code = '''#!/usr/bin/env python3
"""
速度测试脚本
"""

import time
import requests
import json

def test_speed():
    """测试 API 响应速度"""
    
    base_url = "http://localhost:8000"
    
    test_cases = [
        {"message": "你好", "max_tokens": 10},
        {"message": "1+1=?", "max_tokens": 5},
        {"message": "今天天气", "max_tokens": 20},
        {"message": "什么是AI", "max_tokens": 50},
    ]
    
    print("🚀 开始速度测试...")
    print("=" * 50)
    
    total_time = 0
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\\n测试 {i}: {test_case['message']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json=test_case,
                timeout=30
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应时间: {elapsed:.2f}s")
                print(f"📝 回复: {data['response'][:50]}...")
                print(f"🔢 Token: {data.get('tokens_used', 0)}")
                
                total_time += elapsed
                success_count += 1
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 错误: {e} (耗时: {elapsed:.2f}s)")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"\\n📊 测试结果:")
        print(f"   成功: {success_count}/{len(test_cases)}")
        print(f"   平均响应时间: {avg_time:.2f}s")
        print(f"   总耗时: {total_time:.2f}s")
        
        if avg_time < 3:
            print("🎉 性能优秀！")
        elif avg_time < 5:
            print("✅ 性能良好")
        else:
            print("⚠️  需要进一步优化")

if __name__ == "__main__":
    test_speed()
'''
    
    with open("speed_test.py", "w", encoding="utf-8") as f:
        f.write(test_code)
    
    print("✅ 速度测试脚本已创建: speed_test.py")

if __name__ == "__main__":
    print("⚡ Qwen LLM Platform 极致性能优化")
    print("=" * 60)
    
    # 1. 极致优化配置
    if extreme_optimize():
        print("\n✅ 配置优化完成")
    
    # 2. 创建快速模型包装器
    create_fast_model_wrapper()
    
    # 3. 创建速度测试
    create_speed_test()
    
    # 4. 系统优化建议
    optimize_system_settings()
    
    print("\n🚀 优化完成！下一步:")
    print("1. 重启服务: ./start_from_root.py")
    print("2. 运行速度测试: python speed_test.py")
    print("3. 根据结果进一步调整")
