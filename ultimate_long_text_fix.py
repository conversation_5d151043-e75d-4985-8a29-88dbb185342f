#!/usr/bin/env python3
"""
终极长文本解决方案
"""

import os
import json

def create_ultimate_config():
    """创建终极长文本配置"""
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🚀 创建终极长文本配置...")
    
    # 终极长文本配置
    ultimate_config = {
        "model_name": config.get("model_name", "Qwen-1.5-4B-Chat"),
        "model_file": config.get("model_file", "qwen1_5-4b-chat-q4_k_m.gguf"),
        "model_path": config.get("model_path", ""),
        
        # 终极长文本参数
        "context_length": 8192,         # 最大上下文
        "max_tokens": 4096,             # 最大输出
        "temperature": 0.9,             # 高创造性
        "top_p": 0.95,                  # 最大采样多样性
        "repeat_penalty": 1.15,         # 强力避免重复
        
        # 性能参数
        "threads": os.cpu_count() or 4,
        "n_batch": 1024,                # 大批处理
        "n_predict": -1,                # 无限制预测
        
        # 内存优化
        "use_mmap": True,
        "use_mlock": False,
        
        # 长文本专用
        "rope_freq_base": 10000,
        "rope_freq_scale": 1.0,
        "mul_mat_q": True,
        
        # 关闭限制
        "gpu_layers": 0,
        "flash_attn": False,
        "logits_all": False,
        "embedding": False,
        
        # 并行设置
        "enable_parallel": True,
        "max_workers": 1,
        "lazy_loading": True
    }
    
    print("📊 终极配置:")
    for key, value in ultimate_config.items():
        if key in ['context_length', 'max_tokens', 'temperature', 'top_p', 'repeat_penalty']:
            old_value = config.get(key, 'unknown')
            print(f"  {key}: {old_value} → {value} ✨")
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(ultimate_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 终极配置已保存")
    return True

def patch_lightweight_pool():
    """修补轻量级池"""
    pool_file = "src/parallel/lightweight_pool.py"
    
    if not os.path.exists(pool_file):
        print("❌ 轻量级池文件不存在")
        return False
    
    print("🔧 修补轻量级池...")
    
    # 读取文件
    with open(pool_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换关键配置
    replacements = [
        # 增加上下文长度
        ('n_ctx=self.config.get("context_length", 4096)', 'n_ctx=self.config.get("context_length", 8192)'),
        
        # 移除所有停止词
        ('stop=["<|endoftext|>", "<|im_end|>"]', 'stop=[]'),
        
        # 增加线程数
        ('n_threads=min(4, os.cpu_count() or 4)', 'n_threads=os.cpu_count() or 4'),
        
        # 增加批处理
        ('n_batch=512', 'n_batch=1024'),
    ]
    
    modified = False
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            modified = True
            print(f"  ✅ 替换: {old} → {new}")
    
    if modified:
        # 备份原文件
        backup_file = pool_file + ".backup"
        with open(backup_file, 'w', encoding='utf-8') as f:
            with open(pool_file, 'r', encoding='utf-8') as orig:
                f.write(orig.read())
        
        # 写入修改后的内容
        with open(pool_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 轻量级池已修补（备份: {backup_file}）")
    else:
        print("⚠️  没有找到需要替换的内容")
    
    return True

def create_no_stop_model():
    """创建无停止词模型"""
    model_code = '''
"""
无停止词模型 - 专门用于长文本生成
"""

import os
import time
import asyncio
from typing import Optional, Dict, Any

try:
    from llama_cpp import Llama
except ImportError:
    Llama = None

class NoStopQwenModel:
    """无停止词 Qwen 模型 - 专门用于长文本"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        self.config = {}
        
    async def load_model(self):
        """加载无停止词模型"""
        print("🚀 加载无停止词长文本模型...")
        
        # 读取配置
        config_path = "models/model_config.json"
        if os.path.exists(config_path):
            import json
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        
        model_path = self.config.get("model_path", "")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        print(f"📥 加载模型: {os.path.basename(model_path)}")
        
        # 在线程池中加载
        loop = asyncio.get_event_loop()
        self.model = await loop.run_in_executor(None, self._load_model_sync)
        
        self.is_loaded = True
        print("✅ 无停止词模型加载完成")
    
    def _load_model_sync(self):
        """同步加载模型"""
        if Llama is None:
            raise ImportError("llama-cpp-python 未安装")
        
        return Llama(
            model_path=self.config["model_path"],
            n_ctx=self.config.get("context_length", 8192),      # 大上下文
            n_threads=self.config.get("threads", os.cpu_count() or 4),
            n_batch=self.config.get("n_batch", 1024),           # 大批处理
            use_mmap=self.config.get("use_mmap", True),
            use_mlock=self.config.get("use_mlock", False),
            verbose=False
        )
    
    async def generate(self, prompt: str, temperature: float = 0.9, 
                      max_tokens: int = 4096, system_prompt: str = None) -> Dict[str, Any]:
        """生成长文本（无停止词）"""
        if not self.is_loaded:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 构建提示
        if system_prompt:
            full_prompt = f"<|im_start|>system\\n{system_prompt}<|im_end|>\\n<|im_start|>user\\n{prompt}<|im_end|>\\n<|im_start|>assistant\\n"
        else:
            full_prompt = f"<|im_start|>user\\n{prompt}<|im_end|>\\n<|im_start|>assistant\\n"
        
        # 在线程池中生成
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, self._generate_sync, full_prompt, temperature, max_tokens
        )
        
        processing_time = time.time() - start_time
        
        return {
            "text": result["choices"][0]["text"].strip(),
            "model": "Qwen-NoStop",
            "tokens_used": result["usage"]["total_tokens"],
            "processing_time": round(processing_time, 2)
        }
    
    def _generate_sync(self, prompt: str, temperature: float, max_tokens: int):
        """同步生成（无停止词）"""
        return self.model(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=self.config.get("top_p", 0.95),
            repeat_penalty=self.config.get("repeat_penalty", 1.15),
            stop=[],  # 无停止词！
            echo=False
        )
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_name": "Qwen-NoStop",
            "is_loaded": self.is_loaded,
            "mode": "long_text_no_stop"
        }
'''
    
    with open("src/models/no_stop_qwen_model.py", "w", encoding="utf-8") as f:
        f.write(model_code)
    
    print("✅ 无停止词模型已创建: src/models/no_stop_qwen_model.py")

def create_long_text_service():
    """创建长文本专用服务"""
    service_code = '''#!/usr/bin/env python3
"""
长文本专用服务
"""

import os
import sys
sys.path.insert(0, 'src')

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import uvicorn

# 导入无停止词模型
from models.no_stop_qwen_model import NoStopQwenModel

app = FastAPI(title="Long Text Qwen Service")
model = None

class LongTextRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 4096
    temperature: Optional[float] = 0.9
    system_prompt: Optional[str] = None

@app.on_event("startup")
async def startup():
    global model
    model = NoStopQwenModel()
    await model.load_model()

@app.post("/long_chat")
async def long_chat(request: LongTextRequest):
    """长文本对话接口"""
    if not model or not model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")
    
    try:
        result = await model.generate(
            prompt=request.message,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            system_prompt=request.system_prompt
        )
        
        return {
            "response": result["text"],
            "model": result["model"],
            "tokens_used": result["tokens_used"],
            "processing_time": result["processing_time"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
'''
    
    with open("long_text_service.py", "w", encoding="utf-8") as f:
        f.write(service_code)
    
    os.chmod("long_text_service.py", 0o755)
    print("✅ 长文本专用服务已创建: long_text_service.py")

def main():
    """主函数"""
    print("🚀 终极长文本解决方案")
    print("=" * 60)
    
    # 1. 创建终极配置
    if create_ultimate_config():
        print("\n✅ 终极配置创建完成")
    
    # 2. 修补轻量级池
    if patch_lightweight_pool():
        print("\n✅ 轻量级池修补完成")
    
    # 3. 创建无停止词模型
    create_no_stop_model()
    
    # 4. 创建长文本专用服务
    create_long_text_service()
    
    print("\n🎯 使用方案:")
    print("方案1 - 修复现有服务:")
    print("  1. 重启现有服务: python fast_start.py")
    print("  2. 测试长文本生成")
    
    print("\n方案2 - 使用专用长文本服务:")
    print("  1. 启动长文本服务: python long_text_service.py")
    print("  2. 访问: http://localhost:8001/long_chat")
    
    print("\n🧪 测试命令:")
    print('curl -X POST "http://localhost:8001/long_chat" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"message": "详细介绍红楼梦", "max_tokens": 2000}\'')

if __name__ == "__main__":
    main()
