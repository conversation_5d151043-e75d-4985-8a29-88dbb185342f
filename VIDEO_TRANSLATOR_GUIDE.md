# 🎬 AI视频翻译系统使用指南

## 📋 功能概述

这是一个完整的AI视频翻译系统，支持：

- **视频上传**: 支持多种格式 (MP4, AVI, MOV, MKV, WebM, FLV, WMV)
- **智能字幕提取**: 自动检测现有字幕或使用Whisper语音识别
- **多语言翻译**: 支持中英日韩法德西俄等多种语言
- **SRT字幕生成**: 生成标准SRT格式字幕文件
- **Web界面**: 美观易用的网页界面

## 🚀 快速开始

### 1. 安装依赖

```bash
# 运行安装脚本
python setup_video_translator.py
```

### 2. 启动服务

```bash
# 启动AI服务
python src/main.py
```

### 3. 访问界面

打开浏览器访问: http://localhost:8000/static/video_translator.html

## 📖 详细使用说明

### 界面功能

1. **文件上传区域**
   - 点击选择文件或直接拖拽视频文件
   - 支持最大500MB的视频文件
   - 显示文件名和大小信息

2. **翻译选项**
   - **目标语言**: 选择要翻译到的语言
   - **源语言**: 选择原始语言（可选自动检测）
   - **强制语音识别**: 即使有现有字幕也重新识别语音

3. **处理过程**
   - 实时显示处理进度
   - 显示每个处理步骤的状态
   - 处理完成后显示结果

4. **结果查看**
   - 查看原始字幕和翻译字幕
   - 下载SRT字幕文件
   - 显示处理统计信息

### 处理流程

1. **文件验证**: 检查文件格式和大小
2. **字幕提取**: 
   - 优先提取视频中的现有字幕
   - 如果没有字幕或选择强制识别，使用Whisper进行语音识别
3. **语音识别**: 使用OpenAI Whisper模型转录音频
4. **字幕翻译**: 使用AI模型翻译字幕内容
5. **结果生成**: 生成SRT格式的字幕文件

## 🔧 技术架构

### 后端组件

- **CompleteVideoTranslator**: 完整的视频翻译器
  - 音频提取 (FFmpeg)
  - 语音识别 (Whisper)
  - 字幕翻译 (Qwen模型)
  - 格式转换 (SRT)

- **API端点**: `/translate_video_complete`
  - 接收视频文件上传
  - 处理翻译请求
  - 返回翻译结果

### 前端界面

- **响应式设计**: 支持桌面和移动设备
- **拖拽上传**: 直观的文件上传体验
- **实时进度**: 显示处理进度和状态
- **结果展示**: 分标签显示原始和翻译字幕

## 📝 API使用示例

### cURL示例

```bash
curl -X POST "http://localhost:8000/translate_video_complete" \
  -F "file=@your_video.mp4" \
  -F "target_language=zh" \
  -F "source_language=auto" \
  -F "force_transcribe=false"
```

### Python示例

```python
import requests

url = "http://localhost:8000/translate_video_complete"

with open("your_video.mp4", "rb") as f:
    files = {"file": f}
    data = {
        "target_language": "zh",
        "source_language": "auto", 
        "force_transcribe": False
    }
    
    response = requests.post(url, files=files, data=data)
    result = response.json()
    
    if result.get("success"):
        print("翻译成功!")
        print("原始字幕:", result["subtitles"]["original"])
        print("翻译字幕:", result["subtitles"]["translated"])
    else:
        print("翻译失败:", result.get("error"))
```

## ⚙️ 配置选项

### 支持的语言

- **中文**: zh
- **英语**: en  
- **日语**: ja
- **韩语**: ko
- **法语**: fr
- **德语**: de
- **西班牙语**: es
- **俄语**: ru

### 支持的视频格式

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WebM (.webm)
- FLV (.flv)
- WMV (.wmv)

### 系统要求

- **Python**: 3.8或更高版本
- **FFmpeg**: 用于音频/视频处理
- **内存**: 建议4GB以上
- **存储**: 临时文件需要额外空间

## 🛠️ 故障排除

### 常见问题

1. **FFmpeg未找到**
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt install ffmpeg
   
   # CentOS/RHEL
   sudo yum install ffmpeg
   ```

2. **Whisper安装失败**
   ```bash
   # 使用信任的主机
   pip install openai-whisper --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org
   ```

3. **内存不足**
   - 使用较小的视频文件
   - 关闭其他应用程序
   - 考虑使用更小的Whisper模型

4. **处理速度慢**
   - 首次使用会下载Whisper模型
   - 视频长度影响处理时间
   - CPU性能影响语音识别速度

### 日志查看

服务运行时会显示详细的处理日志，包括：
- 文件上传状态
- 音频提取进度
- 语音识别结果
- 翻译处理状态
- 错误信息和调试信息

## 📊 性能优化

### 建议设置

1. **视频文件**
   - 文件大小: < 500MB
   - 时长: < 30分钟
   - 音频质量: 清晰无噪音

2. **系统配置**
   - 使用SSD存储
   - 充足的内存空间
   - 稳定的网络连接

3. **模型选择**
   - tiny: 最快，准确度较低
   - base: 平衡速度和准确度
   - small/medium/large: 更准确但更慢

## 🔒 安全注意事项

- 上传的视频文件会临时存储在服务器
- 处理完成后会自动清理临时文件
- 建议在私有网络环境中使用
- 敏感内容请谨慎处理

## 📞 技术支持

如果遇到问题，请检查：

1. 系统依赖是否正确安装
2. 网络连接是否正常
3. 文件格式是否支持
4. 服务日志中的错误信息

## 🎯 未来功能

计划中的功能改进：

- 批量视频处理
- 更多语言支持
- 字幕样式自定义
- 视频预览功能
- 云端部署支持
