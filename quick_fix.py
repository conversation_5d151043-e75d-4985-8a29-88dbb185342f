#!/usr/bin/env python3
"""
快速修复响应格式问题
"""

import requests
import time

def test_simple_prompt():
    """测试简单提示"""
    print("🧪 测试简单提示格式...")
    
    # 使用最简单的提示
    simple_tests = [
        {
            "message": "Hello",
            "max_tokens": 10,
            "temperature": 0.1
        },
        {
            "message": "What is 1+1?",
            "max_tokens": 5,
            "temperature": 0.1
        },
        {
            "message": "Hi there",
            "max_tokens": 15,
            "temperature": 0.1
        }
    ]
    
    for i, test in enumerate(simple_tests, 1):
        print(f"\n测试 {i}: '{test['message']}'")
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json=test,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                print(f"原始响应: '{response_text}'")
                
                # 清理响应
                cleaned = response_text.replace('\\n', '\n').replace('\\\\', '\\').strip()
                print(f"清理后: '{cleaned}'")
                
                if cleaned and not cleaned.startswith('\n'):
                    print("✅ 响应正常")
                else:
                    print("❌ 响应仍有问题")
                    
                print(f"Token: {data.get('tokens_used', 0)}")
                print(f"时间: {data.get('processing_time', 0)}s")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        time.sleep(1)

def test_with_system_prompt():
    """测试系统提示"""
    print("\n🧪 测试系统提示...")
    
    test_data = {
        "message": "Hello",
        "max_tokens": 20,
        "temperature": 0.1,
        "system_prompt": "You are a helpful assistant. Please respond in English."
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            
            print(f"系统提示响应: '{response_text}'")
            
            cleaned = response_text.replace('\\n', '\n').replace('\\\\', '\\').strip()
            print(f"清理后: '{cleaned}'")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def check_cache_performance():
    """检查缓存性能"""
    print("\n🧪 测试缓存性能...")
    
    test_data = {
        "message": "Hello world",
        "max_tokens": 10,
        "temperature": 0.1
    }
    
    times = []
    
    for i in range(3):
        print(f"\n请求 {i+1}:")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json=test_data,
                timeout=10
            )
            
            elapsed = time.time() - start_time
            times.append(elapsed)
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应时间: {elapsed:.2f}s")
                print(f"处理时间: {data.get('processing_time', 0)}s")
                
                if elapsed < 0.1:
                    print("💾 可能来自缓存")
                else:
                    print("🔄 新生成")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        time.sleep(0.5)
    
    if len(times) >= 2:
        print(f"\n📊 缓存效果:")
        print(f"首次: {times[0]:.2f}s")
        print(f"缓存: {times[1]:.2f}s")
        if times[1] > 0:
            speedup = times[0] / times[1]
            print(f"加速: {speedup:.1f}x")

def get_stats():
    """获取系统统计"""
    print("\n📊 获取系统统计...")
    
    try:
        response = requests.get("http://localhost:8000/stats", timeout=5)
        
        if response.status_code == 200:
            stats = response.json()
            
            if "model" in stats:
                model_stats = stats["model"]
                print("模型统计:")
                for key, value in model_stats.items():
                    print(f"  {key}: {value}")
            
            if "pool_stats" in stats.get("model", {}):
                pool_stats = stats["model"]["pool_stats"]
                print("\n并行池统计:")
                for key, value in pool_stats.items():
                    print(f"  {key}: {value}")
                    
        else:
            print(f"❌ 获取统计失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("🔧 Qwen LLM Platform 快速修复测试")
    print("=" * 50)
    
    # 检查服务状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务不可用")
            return
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        return
    
    print("✅ 服务连接正常")
    
    # 运行测试
    test_simple_prompt()
    test_with_system_prompt()
    check_cache_performance()
    get_stats()
    
    print("\n" + "=" * 50)
    print("🎯 修复建议:")
    print("1. 如果响应仍有问题，重启服务")
    print("2. 检查提示格式是否正确")
    print("3. 调整停止词设置")
    print("4. 验证缓存功能正常")

if __name__ == "__main__":
    main()
