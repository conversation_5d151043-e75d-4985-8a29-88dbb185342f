PIL/.dylibs/libXau.6.0.0.dylib,sha256=dzyMe832zYlJeUYfIjwiCvR0gJgCRcaBCZvuZNSO0Xw,70048
PIL/.dylibs/libbrotlicommon.1.1.0.dylib,sha256=neNJ4ideqXAuP2nuW64mRekfacBHTWZ04LEm6-Om0wU,201200
PIL/.dylibs/libbrotlidec.1.1.0.dylib,sha256=KWwh9jBZ--LEJrSr_n7ZJ0eXpb_4db1Y3XXjYsESHFY,104576
PIL/.dylibs/libfreetype.6.dylib,sha256=_6iyuBWubhhrADfLoaryBId8aR1rFF57-RSotu_Wcsw,1208416
PIL/.dylibs/libharfbuzz.0.dylib,sha256=rx7JWAa2sUHqgeKSHf7LBTY7iLJiZYQVITKMthsmrzQ,1350416
PIL/.dylibs/libjpeg.62.4.0.dylib,sha256=5lKyBDTYhkzbnbE6n-iMroY7Z6owmgnf489PKn9lm4w,637072
PIL/.dylibs/liblcms2.2.dylib,sha256=SIvJPlp1MZLwkz44GNb2bkxlv_eHJU90dxxmUVfDjhQ,557344
PIL/.dylibs/liblzma.5.dylib,sha256=1PP8wABbt_y_8azt0TSmYpWJs6td9E13IBXr_ofneO0,341168
PIL/.dylibs/libopenjp2.2.5.2.dylib,sha256=F8SngWFvofU0GGc5UZDpK-mEOOvQpqK5RLB8umzRSC4,699376
PIL/.dylibs/libpng16.16.dylib,sha256=M6wr4oF-7TfwEX_m3kl3-uppRoqwkwFSv1ZU8AtSfMI,361456
PIL/.dylibs/libsharpyuv.0.dylib,sha256=3xxvoU4B5cgQj4syLW1qju41eF6nuJbr98fCwXlgkzY,86256
PIL/.dylibs/libtiff.6.dylib,sha256=XJQVEatp7X0P0O2huqKsxUmW0esP6TlV7d2CX_EtE_k,802784
PIL/.dylibs/libwebp.7.dylib,sha256=IWhYgvJL6xI4FWXzdr0EvlAQT-IW-NCLFHabLpZh0_8,515104
PIL/.dylibs/libwebpdemux.2.dylib,sha256=bXj1WFHvIEuztpUiVo8be4SyYjOTkX98JqFLxKGZZJc,69904
PIL/.dylibs/libwebpmux.3.dylib,sha256=Mju_z1Nx9iNlJH92BcorQLnTmbCvVzmL0XS_qUHQbWs,106400
PIL/.dylibs/libxcb.1.1.0.dylib,sha256=pk16FsEueNHZxquiiqNWvXBxkNjuig25t7NrWruwEuQ,277696
PIL/.dylibs/libz.1.3.1.dylib,sha256=mT_KRbPa44cfvoWyIzeIFhlTaISEePTB-5AvdcUG8iA,174848
PIL/BdfFontFile.py,sha256=Hnlsd7gTtt7gaY1ilXHP-co5ehkBQ8xml942d06MEno,3477
PIL/BlpImagePlugin.py,sha256=3Mi0H6V4xduNuZXGuFZ0xHaJEWyrhaZJFZ_pAO6wfVk,16584
PIL/BmpImagePlugin.py,sha256=3li_kaQt9Q6-xvIFQ6tP_g_UkpbOadqdKDNlqBzT9Fg,19758
PIL/BufrStubImagePlugin.py,sha256=PcwNafLqxrQ_RTsz7hAqfpLWcQHT-kXKYPLaB46ZtUA,1753
PIL/ContainerIO.py,sha256=wkBqL2GDAb5fh3wrtfTGUfqioJipCl-lg2GxbjQrTZw,4604
PIL/CurImagePlugin.py,sha256=xTou-ULQPpI5lVdqpQ2-4Pdjnh7j1GddH-K22DQbdk0,1792
PIL/DcxImagePlugin.py,sha256=K70qkwz-e3nkgjftD5pIElMcU2klHWFTKbINlwgeQ24,2034
PIL/DdsImagePlugin.py,sha256=Wl4E5GyLUPmm23hSP7tFuTUw9ADgnwRhxx1f3Lxthyc,16960
PIL/EpsImagePlugin.py,sha256=NFR3P7AWhWLH8y1jWsB7hnezY9nUiAKYKc-GUaLq6MQ,16374
PIL/ExifTags.py,sha256=bMq60WOnExBhe-5A_in04puhqZq4E3Bgt5e5k5Hre-o,9753
PIL/FitsImagePlugin.py,sha256=QVW0dCJpPCrV2N6qSHVS9nXpjYPHnbHWxSC88LcYDN0,4639
PIL/FliImagePlugin.py,sha256=dgyGVcNdsRSJ1Hr9uhLPCsM3q67eaJJV5TltMCrvbTY,4681
PIL/FontFile.py,sha256=St7MxO5Q-oakCLWn3ZrgrtaT3wSsmAarxm8AU-G8Moc,3577
PIL/FpxImagePlugin.py,sha256=3eN5yRj30UMe2sEOqaOUdLqM9Fbg2WAVAGastpeA48I,7291
PIL/FtexImagePlugin.py,sha256=0i-i-w44ai9OSF2vkQKaJUr1A2Ypx53MeQra_WPzWzc,3535
PIL/GbrImagePlugin.py,sha256=5t0UfLubTPQcuDDbafwC78OLR7IsD5hjpvhUZ5g8z4A,3006
PIL/GdImageFile.py,sha256=5AwaVs57XjOYI96ENUNFpCgvOVoGuvvaIf-3JwJPNeg,2810
PIL/GifImagePlugin.py,sha256=wudsFRH_Di0NWj5VepqDEETGWu3jwUnbw4F43yD4Mos,41415
PIL/GimpGradientFile.py,sha256=ABNhtD50Gv82Dn1VxbdgfSIz3Q2_nPiv_zDquOYyVAw,3898
PIL/GimpPaletteFile.py,sha256=mK8RqdS7Ae9W7gZ7NB7MkyzuOqhC6Z09_OsLkxCJoak,1427
PIL/GribStubImagePlugin.py,sha256=hzsipSut7wvQ14P5fx7mkGtLj2TWKZk7OwSiFstB194,1747
PIL/Hdf5StubImagePlugin.py,sha256=6bSeB8RJaWqdU3-xwnJIWtGBZjpM0QnpcM8974UWN90,1750
PIL/IcnsImagePlugin.py,sha256=mRXkUrNlnKB8vyW9UphHJ4JefgIrpzIqaUamf76uPec,12953
PIL/IcoImagePlugin.py,sha256=-6GillVbyW9nWlC83IKV7F-99O2aF01U7B1EuxGZpgY,12468
PIL/ImImagePlugin.py,sha256=WFU1D3Cla9CDOJpK5S8Dyxr7bnYVKZffW702t4PeUU8,11429
PIL/Image.py,sha256=R1EsUZFRcg-xt0Dwx9Lfa88qJXanDKY5OgwGMZfvvhI,145965
PIL/ImageChops.py,sha256=GEjlymcoDtA5OOeIxQVIX96BD-s6AXhb7TmSLYn2tUg,7946
PIL/ImageCms.py,sha256=wpVg1Kmp5WfeCNbEfGUCZsjcWVreg3HZqMHyTttlz1s,42010
PIL/ImageColor.py,sha256=IGA9C2umeED_EzS2Cvj6KsU0VutC9RstWIYPe8uDsVk,9441
PIL/ImageDraw.py,sha256=7TZ0miXREA8vFh0yCCAy3k0olUfYz8erDjM4-AH586o,42275
PIL/ImageDraw2.py,sha256=pdVMW7bVw3KwhXvRZh28Md4y-2xFfuo5fHcDnaYqVK4,7227
PIL/ImageEnhance.py,sha256=4Elhz_lyyxLmx0GkSHrwOAmNJ2TkqVQPHejzGihZUMI,3627
PIL/ImageFile.py,sha256=Bz3X53qOWOY2QdR9c5dxSn1FkO08Ais1LKQB6s4iCCI,26145
PIL/ImageFilter.py,sha256=FphC23usjP3k27kQ7Mw3ryPXaTiVKaBQmnqyuzpKCbY,18697
PIL/ImageFont.py,sha256=orY3-Zx6EnRiNmI9qI0-a-8JHco4zh2l91kQWkzKpg4,64279
PIL/ImageGrab.py,sha256=B0ujDFUOCKusImJtXtMXXqKbs9YKr21ujfIt1hSy8qQ,6358
PIL/ImageMath.py,sha256=qDVyqP24n4FnCgJRgW_DVcRFoTdZFJLQd5qxAZS4EG4,11942
PIL/ImageMode.py,sha256=5yOxODAZ7jG03DsUFrt7eQayTtIpWPgvfyhlXDWwcv8,2681
PIL/ImageMorph.py,sha256=TowXnk1Q2wX9AXVBDWRRQhCfAbFOUWGMo00vq4yn-fU,8563
PIL/ImageOps.py,sha256=yuWy9QMMqFtshgq6ZNERyVc0UIlOUq38M6tOBnsWyKQ,25075
PIL/ImagePalette.py,sha256=wTokkN4dylYRAzxirCmh6M_GyyqbkDazwfX2tEoohCs,9002
PIL/ImagePath.py,sha256=5yUG5XCUil1KKTTA_8PgGhcmg-mnue-GK0FwTBlhjw4,371
PIL/ImageQt.py,sha256=9DBS5G6SYumDfR2-j_-NJpFw-jQ8zOvYkwGgBqDtMOo,6764
PIL/ImageSequence.py,sha256=gx2EvywPBEjxNJujCqdpbfAm2BpyNV2_f1IaO3niubw,2200
PIL/ImageShow.py,sha256=LBGhPR3k5Z20S7vDyCsL0BftIX5tvTvCd5xdCvA7lTc,9993
PIL/ImageStat.py,sha256=S43FZ89r_u4hKCj59lVuWpyVJfhbUy3igXkp9DwaMgM,5325
PIL/ImageTk.py,sha256=JuzOgUMKiAhR8JAYCSY1Il3iwQ8Hx-vwC4ng_KRKfCQ,8997
PIL/ImageTransform.py,sha256=okpZipXf2u7tDB3dticLozrOKI8QNIsniCq_J4CxQC0,3886
PIL/ImageWin.py,sha256=LT05w8_vTfRrC3n9S9pM0TNbXrzZLEJHlCJil7Xv80k,8085
PIL/ImtImagePlugin.py,sha256=Cch0sZdrgDIrLZXC9PTsJLHyEg7oQFwdYrgTu5sy5mc,2673
PIL/IptcImagePlugin.py,sha256=zMOEYveSc8ph1WdJtC-tUJEueDcInpVUviCcnqKXq0Q,6669
PIL/Jpeg2KImagePlugin.py,sha256=m69h5_tpJDI1KXXfLzfmRshwASpKI9JERjvssaOU2uY,13816
PIL/JpegImagePlugin.py,sha256=W4bts_7z74PZzPmL2I9VBe7KY2eZnUJEiL97hw7vNQQ,31433
PIL/JpegPresets.py,sha256=lnqWHo4DLIHIulcdHp0NJ7CWexHt8T3w51kIKlLfkIA,12379
PIL/McIdasImagePlugin.py,sha256=G_sNQybucqISdmU-XGCtdIcm4jZTI59tcSz96WiUwDI,1938
PIL/MicImagePlugin.py,sha256=8EqZ-Vm9FK23XB-5thR3dWEcq4j3XbMG961-ILRnC0g,2680
PIL/MpegImagePlugin.py,sha256=AplKMsPC9Y9ExJfqTE_xlm8TW-CSx578NGQWyVcmIiQ,2100
PIL/MpoImagePlugin.py,sha256=QVkZnrOGAlPCARpraeNSS6Q-ymQXfQGKAUAfRWTDZMA,6220
PIL/MspImagePlugin.py,sha256=8FVcjSVEA-Hfwto6SPU0NrQUvErhLhmHDxRhww-2pOA,5904
PIL/PSDraw.py,sha256=3hY8wDQamJr5X5dS8pwQ9eUMJAV835of7aX3t8kM5Q8,6909
PIL/PaletteFile.py,sha256=rC4YrlwwpJtl7RdPDnfl21HR4Vge3cAUw5Z6zicBqIk,1211
PIL/PalmImagePlugin.py,sha256=Lz2yNR9099-cjTs4NY-0XvHxxCDBSYJkqXJltcZkNXQ,9351
PIL/PcdImagePlugin.py,sha256=MyYFc2rTlfbZ1QerocnxMknboHDDyWfo-7unLr99W7U,1598
PIL/PcfFontFile.py,sha256=NPZQ0XkbGB8uTlGqgmIPGkwuLMYBdykDeVuvFgIC7JU,7147
PIL/PcxImagePlugin.py,sha256=Uvgh8Nf2Fcc1I1aC73HG2uY4RabjGJAFcIOjul6gyTI,6249
PIL/PdfImagePlugin.py,sha256=AbJA2f4qzH8G1olfmk18SzQlcx3WsipUYDc5bcR8Wvk,9349
PIL/PdfParser.py,sha256=0p4yxf90wHEx1jDRnjpKxjwfDqUYO463kaYS31PJpYY,37980
PIL/PixarImagePlugin.py,sha256=CzGjkhpGxEix8cX6E4BWuW2pzeETa_amD1x5P8ACego,1783
PIL/PngImagePlugin.py,sha256=SPKJvNceXt78CF5U28YrjGJpPRTlNWv8kgCukjsqaHg,50865
PIL/PpmImagePlugin.py,sha256=m2PDVO97GAn5kISON3-PJENWU3WZOiwRbPjiUp_rK0M,12354
PIL/PsdImagePlugin.py,sha256=5g-l_HrIWMG7xSAb4ofhgKVhsnK2yqh6ee9xE-Z1620,8621
PIL/QoiImagePlugin.py,sha256=Ug-vZXovW4-Nmtt8pRKFw0aKdUaNz5qpEpmpL5DEt4k,4189
PIL/SgiImagePlugin.py,sha256=wjO3mgTO7AYC2Bs6RJBEKafm49wgFkCXZuVoBD6UWxc,6732
PIL/SpiderImagePlugin.py,sha256=8bRQV8KeodwYR2qAvem4Jxzg6Cmy1sAI7AdluRdigec,10113
PIL/SunImagePlugin.py,sha256=Hdxkhk0pxpBGxYhPJfCDLwsYcO1KjxjtplNMFYibIvk,4589
PIL/TarIO.py,sha256=uQ5Zh47x67H9fq8iGLSeCfk22i0E7Ae06fVC2bf1LcU,1376
PIL/TgaImagePlugin.py,sha256=2vDsFTcBUBHw1V80wpVv4tgpLDbPr6yVHi6Fvaqf0HY,6980
PIL/TiffImagePlugin.py,sha256=Ykj7B58Xvye-J1eSlC3c9yEb3oAvWqdcUSI-ASYaa5s,82151
PIL/TiffTags.py,sha256=-gbXLZ5rlHD6crwtY6TkafDm2tamlc5v8e7FjS8PcIg,17082
PIL/WalImageFile.py,sha256=Lfuq_WZ_V_onwucfUc6GWfvY7z_K4s-5EdaQGu_2DD4,5704
PIL/WebPImagePlugin.py,sha256=Po7fjkHlR3jDTPtUgo1DV139UGSOjkw_M9e-dSix1HE,10084
PIL/WmfImagePlugin.py,sha256=60VCclVBU2UY9XlbOUuqpn7v_eLr1OtWNpCYvfybob0,5030
PIL/XVThumbImagePlugin.py,sha256=f9YyaqngJFtt8iPfjlN4k6Fp5ABZprJBKLc4Sz96GPY,2148
PIL/XbmImagePlugin.py,sha256=OlaTDPErPd5JCe_B_o0nfWBedb-mTI3mC1Af8iVdGSI,2679
PIL/XpmImagePlugin.py,sha256=upNDrctfv_rGxWuR2W0HlEuG4nVob-bHkEAEv5WZ160,3256
PIL/__init__.py,sha256=fJUwPGhI8_mcB8jNWD-hUw7MiMJyWgqVX_nFtzIj1Zs,2008
PIL/__main__.py,sha256=Lpj4vef8mI7jA1sRCUAoVYaeePD_Uc898xF5c7XLx1A,133
PIL/__pycache__/BdfFontFile.cpython-311.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-311.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-311.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ContainerIO.cpython-311.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-311.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ExifTags.cpython-311.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FontFile.cpython-311.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GdImageFile.cpython-311.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-311.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-311.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-311.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-311.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Image.cpython-311.pyc,,
PIL/__pycache__/ImageChops.cpython-311.pyc,,
PIL/__pycache__/ImageCms.cpython-311.pyc,,
PIL/__pycache__/ImageColor.cpython-311.pyc,,
PIL/__pycache__/ImageDraw.cpython-311.pyc,,
PIL/__pycache__/ImageDraw2.cpython-311.pyc,,
PIL/__pycache__/ImageEnhance.cpython-311.pyc,,
PIL/__pycache__/ImageFile.cpython-311.pyc,,
PIL/__pycache__/ImageFilter.cpython-311.pyc,,
PIL/__pycache__/ImageFont.cpython-311.pyc,,
PIL/__pycache__/ImageGrab.cpython-311.pyc,,
PIL/__pycache__/ImageMath.cpython-311.pyc,,
PIL/__pycache__/ImageMode.cpython-311.pyc,,
PIL/__pycache__/ImageMorph.cpython-311.pyc,,
PIL/__pycache__/ImageOps.cpython-311.pyc,,
PIL/__pycache__/ImagePalette.cpython-311.pyc,,
PIL/__pycache__/ImagePath.cpython-311.pyc,,
PIL/__pycache__/ImageQt.cpython-311.pyc,,
PIL/__pycache__/ImageSequence.cpython-311.pyc,,
PIL/__pycache__/ImageShow.cpython-311.pyc,,
PIL/__pycache__/ImageStat.cpython-311.pyc,,
PIL/__pycache__/ImageTk.cpython-311.pyc,,
PIL/__pycache__/ImageTransform.cpython-311.pyc,,
PIL/__pycache__/ImageWin.cpython-311.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-311.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-311.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-311.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-311.pyc,,
PIL/__pycache__/JpegPresets.cpython-311.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-311.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PSDraw.cpython-311.pyc,,
PIL/__pycache__/PaletteFile.cpython-311.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PcfFontFile.cpython-311.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PdfParser.cpython-311.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-311.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-311.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TarIO.cpython-311.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-311.pyc,,
PIL/__pycache__/TiffTags.cpython-311.pyc,,
PIL/__pycache__/WalImageFile.cpython-311.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-311.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-311.pyc,,
PIL/__pycache__/__init__.cpython-311.pyc,,
PIL/__pycache__/__main__.cpython-311.pyc,,
PIL/__pycache__/_binary.cpython-311.pyc,,
PIL/__pycache__/_deprecate.cpython-311.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-311.pyc,,
PIL/__pycache__/_typing.cpython-311.pyc,,
PIL/__pycache__/_util.cpython-311.pyc,,
PIL/__pycache__/_version.cpython-311.pyc,,
PIL/__pycache__/features.cpython-311.pyc,,
PIL/__pycache__/report.cpython-311.pyc,,
PIL/_binary.py,sha256=pcM6AL04GxgmGeLfcH1V1BZHENwIrQH0uxhJ7r0HIL0,2550
PIL/_deprecate.py,sha256=SLU2p8O9ImHYHsD4VFGKLTkewh_Eda0axfIWUCnkKSg,1936
PIL/_imaging.cpython-311-darwin.so,sha256=F50sXimg1RFUtpAG85T_jrqNq9jGHgU8-wNA3A4-uns,553728
PIL/_imaging.pyi,sha256=StMbXUZS32AegATP1sUHfs5P05A3TD_BiQKsDHQBW40,868
PIL/_imagingcms.cpython-311-darwin.so,sha256=ZJpVJAau3dhmqyCv23vJT6h1lqUumpuLmUpffhHcoRk,80816
PIL/_imagingcms.pyi,sha256=brpjxRoiY_2ItyfTrjhKeGEsExe4GPG-25q9AQP8Jp8,4389
PIL/_imagingft.cpython-311-darwin.so,sha256=s3CqujIJNSz0UOJlz2KhJ8jfIw_48KTbgsDZGvfp_18,116384
PIL/_imagingft.pyi,sha256=62nD4AzNDHddKXgcxblOrxKwu8w7TJIHNM-mqC2fue0,1789
PIL/_imagingmath.cpython-311-darwin.so,sha256=niX9vmxmS7jQV8dCoZG3krRzOPHxHQmwPX7BCviesXA,72112
PIL/_imagingmath.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingmorph.cpython-311-darwin.so,sha256=9bmTBwlGMHygKvi549QYwP2Y_oRCtNddX8wPeoj8z84,51424
PIL/_imagingmorph.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingtk.cpython-311-darwin.so,sha256=6xKK4r4jlneSp5e5lAWlPuJtGhvr13G7oE8u_wFxCw0,52720
PIL/_imagingtk.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_tkinter_finder.py,sha256=CECvYrzWNc7BuzzR_mWZZKjPdADg6iRG8ilJToyjD3w,540
PIL/_typing.py,sha256=1Z6f4jReSU5DGewL-rlD8um0ffMZC_2dKVxeIgrcnuk,1247
PIL/_util.py,sha256=E76J1WLAe6Xg5yNWYztQwYzxUT_sR_VQxFJu7IZ3S3k,635
PIL/_version.py,sha256=qnkPlBT-9uDbZQwnYDTdeQqi7knfSu9eHmjjbot5BKg,87
PIL/_webp.cpython-311-darwin.so,sha256=HMloxN_x5JVphF3M_VcoVjutk7Tn9xWo7jWTATo04v0,75072
PIL/_webp.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/features.py,sha256=RGGVKo5njHUBpq2gX04x9PpjSaIaSs4XC2WCfGOF6xU,10968
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=4JY6-IU7sH1RKuRbOvy1fUt0dAoi79FX4tYJN3p1DT0,100
pillow-11.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-11.0.0.dist-info/LICENSE,sha256=DO-Z-d_U91IsODM1lUFv-5YCPbK4M4_hCIpb_JdwUuQ,60073
pillow-11.0.0.dist-info/METADATA,sha256=Bl6W3gqjvJoCyz7QKJRfJ-PhK6Dz_p9e-VGTHM8U85Y,9110
pillow-11.0.0.dist-info/RECORD,,
pillow-11.0.0.dist-info/WHEEL,sha256=PF0Mgwtv_oDs_mWYUtLpGhYaIa8jOhCqBxSqdxy2gqE,109
pillow-11.0.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-11.0.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
