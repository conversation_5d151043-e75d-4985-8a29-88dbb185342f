MATH_UNARY(sin, double, double)
MATH_UNARY(sinf, float, float)

MATH_UNARY(cos, double, double)
MATH_UNARY(cosf, float, float)

MATH_UNARY(tan, double, double)
MATH_UNARY(tanf, float, float)

MATH_UNARY(sinh, double, double)
MATH_UNARY(sinhf, float, float)

MATH_UNARY(cosh, double, double)
MATH_UNARY(coshf, float, float)

MATH_UNARY(tanh, double, double)
MATH_UNARY(tanhf, float, float)

MATH_UNARY(asin, double, double)
MATH_UNARY(asinf, float, float)

MATH_UNARY(acos, double, double)
MATH_UNARY(acosf, float, float)

MATH_UNARY(atan, double, double)
MATH_UNARY(atanf, float, float)

MATH_UNARY(asinh, double, double)
MATH_UNARY(asinhf, float, float)

MATH_UNARY(acosh, double, double)
MATH_UNARY(acoshf, float, float)

MATH_UNARY(atanh, double, double)
MATH_UNARY(atanhf, float, float)

MATH_UNARY(exp, double, double)
MATH_UNARY(expf, float, float)

MATH_UNARY(expm1, double, double)
MATH_UNARY(expm1f, float, float)

MATH_UNARY(sqrt, double, double)
MATH_UNARY(sqrtf, float, float)

MATH_UNARY(fabs, double, double)
MATH_UNARY(fabsf, float, float)

MATH_UNARY(floor, double, double)
MATH_UNARY(floorf, float, float)

MATH_UNARY(ceil, double, double)
MATH_UNARY(ceilf, float, float)

MATH_UNARY(log, double, double)
MATH_UNARY(logf, float, float)

MATH_UNARY(log10, double, double)
MATH_UNARY(log10f, float, float)

MATH_UNARY(log1p, double, double)
MATH_UNARY(log1pf, float, float)

MATH_UNARY(round, double, double)
MATH_UNARY(roundf, float, float)

MATH_UNARY(trunc, double, double)
MATH_UNARY(truncf, float, float)

MATH_BINARY(pow, double, double, double)
MATH_BINARY(powf, float, float, float)

MATH_BINARY(fmod, double, double, double)
MATH_BINARY(fmodf, float, float, float)

MATH_BINARY(atan2, double, double, double)
MATH_BINARY(atan2f, float, float, float)
