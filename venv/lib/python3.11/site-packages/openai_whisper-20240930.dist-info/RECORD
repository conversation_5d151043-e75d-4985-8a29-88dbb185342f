../../../bin/whisper,sha256=_8TXLRE3tESsQZjqUKChMVhYDIkziJVgMFKRPIffz1Q,243
openai_whisper-20240930.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai_whisper-20240930.dist-info/METADATA,sha256=FmVcK18T2ILEubWBAeklm6oA5-drohIkNYSSokrjJKw,8779
openai_whisper-20240930.dist-info/RECORD,,
openai_whisper-20240930.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai_whisper-20240930.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
openai_whisper-20240930.dist-info/entry_points.txt,sha256=1OTVp0DSZHyPxFEkpZPbYPXpKAw2SsO-M4Ap-VLrSlQ,51
openai_whisper-20240930.dist-info/licenses/LICENSE,sha256=tdZaWQYOaMT_lA4e3fpvlLLWj99Y7X9N1XchyZfjXp0,1063
openai_whisper-20240930.dist-info/top_level.txt,sha256=e73_hD7PzIrqq5Qkj8xc0C3FVPiu7AOQae35YeSGDfQ,8
whisper/__init__.py,sha256=XSnqxTmvgBofkluWC4BuyuHODLPyb5aNdNQMlX8QBEM,7343
whisper/__main__.py,sha256=xRnVhsOmtjn2TlnS1v1rWRpn7Nc5-XJ0kpEfxM5NVFo,35
whisper/__pycache__/__init__.cpython-311.pyc,,
whisper/__pycache__/__main__.cpython-311.pyc,,
whisper/__pycache__/audio.cpython-311.pyc,,
whisper/__pycache__/decoding.cpython-311.pyc,,
whisper/__pycache__/model.cpython-311.pyc,,
whisper/__pycache__/timing.cpython-311.pyc,,
whisper/__pycache__/tokenizer.cpython-311.pyc,,
whisper/__pycache__/transcribe.cpython-311.pyc,,
whisper/__pycache__/triton_ops.cpython-311.pyc,,
whisper/__pycache__/utils.cpython-311.pyc,,
whisper/__pycache__/version.cpython-311.pyc,,
whisper/assets/gpt2.tiktoken,sha256=MGzSfwPBpxTspxCOA9ZrfcBCq-jCWLRMGZp-2YON2TA,835554
whisper/assets/mel_filters.npz,sha256=dFCucHI6XvnTQePO5ijHywF382zkLES37SvzMl8PbUw,4271
whisper/assets/multilingual.tiktoken,sha256=s0s2DbtJPngeR5eUWG1mFwBnDWVWQAHyMCSXHR8voSY,816730
whisper/audio.py,sha256=2TslvajXAWMHZPNI0wI9H2209OpeFwjVVu0rEdKwHfI,4932
whisper/decoding.py,sha256=lAhP7cWvdMq_jhiDqFVScABXCB8A6maa1JQ17LKQOds,32155
whisper/model.py,sha256=RzSRqXVUrR0L6q72_pHZZfEnhbRmuT67YQJDB7UK1oM,11749
whisper/normalizers/__init__.py,sha256=8Y_Nzkyu5_LoDkwBqwRX06CAqHKilIlULG0jxLC7pXI,130
whisper/normalizers/__pycache__/__init__.cpython-311.pyc,,
whisper/normalizers/__pycache__/basic.cpython-311.pyc,,
whisper/normalizers/__pycache__/english.cpython-311.pyc,,
whisper/normalizers/basic.py,sha256=rVZ7Og4dA8zAw1_HRv1oMMNSkP5O12dOthzhV7o9OA0,1936
whisper/normalizers/english.json,sha256=Zgf5SL6YJNLhsvoiI82UwGxFr6TgXqDj1eHyvf_eJGU,56128
whisper/normalizers/english.py,sha256=KJjkZyru_J9Ey03jjBFc3yhvWzWuMhQzRnp2dM6IQdA,20868
whisper/timing.py,sha256=yMAeV89nd-eKTH_pg2nYTRWVwn91wsj2B5fwfTDjjXI,12691
whisper/tokenizer.py,sha256=O0jjYafpW07ANWym1yu6Y1d4qhAmkVMTbue8NMrjC4U,12338
whisper/transcribe.py,sha256=CVfTMIVnwn_p88hXefz80UsWq7eeGNNTy-GJspoCcIk,29302
whisper/triton_ops.py,sha256=FkyDF7qsWkFWFSBuSJ4SvHvetsQ7pPyhuyDGJvjNyVc,3474
whisper/utils.py,sha256=M2Bs2c_iEuqSstPnQweG-Wc7uvbdvVCxnY1ohC0Yyx4,11449
whisper/version.py,sha256=fh-a0GSHgBLEn0puaxcvyL59cgaINLWK0usOu04ofOM,25
