<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 AI视频翻译系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #667eea;
            background: #f0f2ff;
        }
        
        .upload-section.dragover {
            border-color: #667eea;
            background: #e8ecff;
            transform: scale(1.02);
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .option-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .option-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .option-group select,
        .option-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-size: 1em;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .process-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.2em;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .process-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
        }
        
        .process-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress-section {
            margin-top: 30px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .step.active {
            background: #e8ecff;
            color: #667eea;
        }
        
        .step.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .step.active .step-icon {
            background: #667eea;
            color: white;
        }
        
        .step.completed .step-icon {
            background: #28a745;
            color: white;
        }
        
        .results-section {
            margin-top: 30px;
            display: none;
        }
        
        .result-tabs {
            display: flex;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }
        
        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 1em;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .subtitle-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .info-card h4 {
            color: #495057;
            margin-bottom: 5px;
        }
        
        .info-card p {
            font-size: 1.2em;
            font-weight: 600;
            color: #667eea;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 10px rgba(23, 162, 184, 0.3);
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 AI视频翻译系统</h1>
            <p>支持视频上传、语音识别、字幕提取和多语言翻译</p>
        </div>
        
        <div class="main-content">
            <!-- 文件上传区域 -->
            <div class="upload-section" id="uploadSection">
                <h3>📁 选择视频文件</h3>
                <p>支持 MP4, AVI, MOV, MKV, WebM, FLV, WMV 格式</p>
                <p>最大文件大小: 500MB</p>
                <br>
                <input type="file" id="videoFile" class="file-input" accept=".mp4,.avi,.mov,.mkv,.webm,.flv,.wmv">
                <button class="upload-btn" onclick="document.getElementById('videoFile').click()">
                    选择文件
                </button>
                <div id="fileName" style="margin-top: 15px; font-weight: 600;"></div>
            </div>
            
            <!-- 选项设置 -->
            <div class="options-grid">
                <div class="option-group">
                    <label for="targetLanguage">目标语言:</label>
                    <select id="targetLanguage">
                        <option value="zh">中文</option>
                        <option value="en">英语</option>
                        <option value="ja">日语</option>
                        <option value="ko">韩语</option>
                        <option value="fr">法语</option>
                        <option value="de">德语</option>
                        <option value="es">西班牙语</option>
                        <option value="ru">俄语</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <label for="sourceLanguage">源语言:</label>
                    <select id="sourceLanguage">
                        <option value="auto">自动检测</option>
                        <option value="en">英语</option>
                        <option value="zh">中文</option>
                        <option value="ja">日语</option>
                        <option value="ko">韩语</option>
                        <option value="fr">法语</option>
                        <option value="de">德语</option>
                        <option value="es">西班牙语</option>
                        <option value="ru">俄语</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="forceTranscribe">
                        <label for="forceTranscribe">强制语音识别</label>
                    </div>
                    <small style="color: #6c757d;">即使视频有字幕也重新识别语音</small>
                </div>
            </div>
            
            <!-- 处理按钮 -->
            <button class="process-btn" id="processBtn" onclick="processVideo()">
                🚀 开始翻译
            </button>
            
            <!-- 进度显示 -->
            <div class="progress-section" id="progressSection">
                <h3>处理进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-steps" id="progressSteps"></div>
            </div>
            
            <!-- 结果显示 -->
            <div class="results-section" id="resultsSection">
                <h3>翻译结果</h3>
                
                <div class="info-grid" id="infoGrid"></div>
                
                <div class="result-tabs">
                    <button class="tab-btn active" onclick="showTab('original')">原始字幕</button>
                    <button class="tab-btn" onclick="showTab('translated')">翻译字幕</button>
                </div>
                
                <div class="tab-content active" id="originalTab">
                    <div class="subtitle-viewer" id="originalSubtitles"></div>
                    <button class="download-btn" onclick="downloadSubtitle('original')">下载原始字幕</button>
                </div>
                
                <div class="tab-content" id="translatedTab">
                    <div class="subtitle-viewer" id="translatedSubtitles"></div>
                    <button class="download-btn" onclick="downloadSubtitle('translated')">下载翻译字幕</button>
                </div>
            </div>
            
            <!-- 消息显示 -->
            <div id="messageArea"></div>
        </div>
    </div>

    <script>
        let currentResult = null;
        
        // 文件拖拽上传
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('videoFile');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                updateFileName();
            }
        });
        
        fileInput.addEventListener('change', updateFileName);
        
        function updateFileName() {
            const fileName = document.getElementById('fileName');
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                fileName.textContent = `已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            } else {
                fileName.textContent = '';
            }
        }
        
        async function processVideo() {
            if (!fileInput.files.length) {
                showMessage('请先选择视频文件', 'error');
                return;
            }
            
            const processBtn = document.getElementById('processBtn');
            const progressSection = document.getElementById('progressSection');
            const resultsSection = document.getElementById('resultsSection');
            
            // 重置界面
            processBtn.disabled = true;
            processBtn.textContent = '处理中...';
            progressSection.style.display = 'block';
            resultsSection.style.display = 'none';
            clearMessage();
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('target_language', document.getElementById('targetLanguage').value);
            formData.append('source_language', document.getElementById('sourceLanguage').value);
            formData.append('force_transcribe', document.getElementById('forceTranscribe').checked);
            
            try {
                // 开始处理
                updateProgress(0, ['开始处理...']);
                
                const response = await fetch('/translate_video_complete', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.detail || '处理失败');
                }
                
                const result = await response.json();
                currentResult = result;
                
                // 显示结果
                displayResults(result);
                showMessage('视频翻译完成！', 'success');
                
            } catch (error) {
                console.error('处理错误:', error);
                showMessage(`处理失败: ${error.message}`, 'error');
            } finally {
                processBtn.disabled = false;
                processBtn.textContent = '🚀 开始翻译';
            }
        }
        
        function updateProgress(percent, steps) {
            const progressFill = document.getElementById('progressFill');
            const progressSteps = document.getElementById('progressSteps');
            
            progressFill.style.width = `${percent}%`;
            
            progressSteps.innerHTML = steps.map((step, index) => `
                <div class="step ${index < steps.length - 1 ? 'completed' : 'active'}">
                    <div class="step-icon">${index < steps.length - 1 ? '✓' : '⏳'}</div>
                    <span>${step}</span>
                </div>
            `).join('');
        }
        
        function displayResults(result) {
            const resultsSection = document.getElementById('resultsSection');
            const infoGrid = document.getElementById('infoGrid');
            const originalSubtitles = document.getElementById('originalSubtitles');
            const translatedSubtitles = document.getElementById('translatedSubtitles');
            
            // 更新进度为完成
            updateProgress(100, [...result.processing_steps, '完成']);
            
            // 显示信息卡片
            infoGrid.innerHTML = `
                <div class="info-card">
                    <h4>文件名</h4>
                    <p>${result.video_info.filename}</p>
                </div>
                <div class="info-card">
                    <h4>文件大小</h4>
                    <p>${result.video_info.size_mb} MB</p>
                </div>
                <div class="info-card">
                    <h4>字幕来源</h4>
                    <p>${result.video_info.subtitle_source === 'existing' ? '现有字幕' : '语音识别'}</p>
                </div>
                <div class="info-card">
                    <h4>源语言</h4>
                    <p>${result.translation_info.source_language}</p>
                </div>
                <div class="info-card">
                    <h4>目标语言</h4>
                    <p>${result.translation_info.target_language}</p>
                </div>
                <div class="info-card">
                    <h4>字幕条数</h4>
                    <p>${result.translation_info.subtitle_count}</p>
                </div>
            `;
            
            // 显示字幕内容
            originalSubtitles.textContent = result.subtitles.original;
            translatedSubtitles.textContent = result.subtitles.translated;
            
            resultsSection.style.display = 'block';
        }
        
        function showTab(tabName) {
            // 更新标签按钮
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + 'Tab').classList.add('active');
        }
        
        function downloadSubtitle(type) {
            if (!currentResult) return;
            
            const content = type === 'original' 
                ? currentResult.subtitles.original 
                : currentResult.subtitles.translated;
            
            const filename = type === 'original'
                ? `${currentResult.video_info.filename}_original.srt`
                : `${currentResult.video_info.filename}_${currentResult.translation_info.target_language}.srt`;
            
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        function showMessage(message, type) {
            const messageArea = document.getElementById('messageArea');
            const className = type === 'error' ? 'error-message' : 'success-message';
            
            messageArea.innerHTML = `
                <div class="${className}">
                    ${message}
                </div>
            `;
        }
        
        function clearMessage() {
            document.getElementById('messageArea').innerHTML = '';
        }
    </script>
</body>
</html>
