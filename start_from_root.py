#!/usr/bin/env python3
"""
从项目根目录启动服务
解决路径问题
"""

import os
import sys
import subprocess

def main():
    # 确保在项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("🚀 从项目根目录启动 Qwen LLM Platform")
    print(f"📁 当前目录: {os.getcwd()}")
    
    # 检查虚拟环境
    venv_python = os.path.join("venv", "bin", "python")
    if os.path.exists(venv_python):
        python_cmd = venv_python
        print("🐍 使用虚拟环境 Python")
    else:
        python_cmd = sys.executable
        print("🐍 使用系统 Python")
    
    # 检查模型文件
    model_file = "models/qwen1_5-4b-chat-q4_k_m.gguf"
    if not os.path.exists(model_file):
        print(f"❌ 模型文件不存在: {model_file}")
        return
    
    print(f"✅ 模型文件存在: {model_file}")
    
    # 设置 Python 路径
    env = os.environ.copy()
    env["PYTHONPATH"] = os.path.join(script_dir, "src")
    
    # 启动服务
    print("🚀 启动服务...")
    print("📖 API 文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("\n按 Ctrl+C 停止服务\n")
    
    try:
        subprocess.run([
            python_cmd, 
            os.path.join("src", "main.py")
        ], env=env, cwd=script_dir)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")

if __name__ == "__main__":
    main()
