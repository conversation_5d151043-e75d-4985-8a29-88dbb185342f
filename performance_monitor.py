#!/usr/bin/env python3
"""
性能监控脚本
"""

import time
import psutil
import requests
import threading
from datetime import datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.stats = []
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        
        def monitor_loop():
            while self.monitoring:
                stats = {
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "memory_used_gb": psutil.virtual_memory().used / (1024**3)
                }
                
                self.stats.append(stats)
                
                if len(self.stats) > 60:  # 保留最近60个数据点
                    self.stats.pop(0)
                
                time.sleep(1)
        
        thread = threading.Thread(target=monitor_loop, daemon=True)
        thread.start()
        print("📊 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        print("📊 性能监控已停止")
    
    def get_stats(self):
        """获取统计信息"""
        if not self.stats:
            return {"message": "暂无数据"}
        
        cpu_avg = sum(s["cpu_percent"] for s in self.stats) / len(self.stats)
        memory_avg = sum(s["memory_percent"] for s in self.stats) / len(self.stats)
        
        return {
            "average_cpu": f"{cpu_avg:.1f}%",
            "average_memory": f"{memory_avg:.1f}%",
            "current_memory_gb": f"{self.stats[-1]['memory_used_gb']:.1f}GB",
            "data_points": len(self.stats)
        }
    
    def benchmark_api(self, api_url="http://localhost:8000"):
        """API 性能基准测试"""
        
        test_cases = [
            {"message": "hi", "max_tokens": 5},
            {"message": "hello", "max_tokens": 10},
            {"message": "what is AI", "max_tokens": 20},
        ]
        
        print("🚀 开始 API 基准测试...")
        
        results = []
        
        for i, test_case in enumerate(test_cases):
            print(f"测试 {i+1}/3: {test_case['message']}")
            
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{api_url}/chat",
                    json=test_case,
                    timeout=30
                )
                
                elapsed = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    results.append({
                        "test": test_case["message"],
                        "response_time": elapsed,
                        "tokens": data.get("tokens_used", 0),
                        "success": True
                    })
                    print(f"✅ 响应时间: {elapsed:.2f}s")
                else:
                    results.append({
                        "test": test_case["message"],
                        "response_time": elapsed,
                        "success": False
                    })
                    print(f"❌ 失败: {response.status_code}")
                    
            except Exception as e:
                elapsed = time.time() - start_time
                results.append({
                    "test": test_case["message"],
                    "response_time": elapsed,
                    "error": str(e),
                    "success": False
                })
                print(f"❌ 错误: {e}")
        
        # 计算平均性能
        successful = [r for r in results if r.get("success")]
        if successful:
            avg_time = sum(r["response_time"] for r in successful) / len(successful)
            avg_tokens = sum(r.get("tokens", 0) for r in successful) / len(successful)
            
            print(f"\n📊 基准测试结果:")
            print(f"   成功率: {len(successful)}/{len(results)}")
            print(f"   平均响应时间: {avg_time:.2f}s")
            print(f"   平均 Token 数: {avg_tokens:.0f}")
            
            if avg_time < 2:
                print("🎉 性能优秀！")
            elif avg_time < 4:
                print("✅ 性能良好")
            else:
                print("⚠️  需要进一步优化")

if __name__ == "__main__":
    monitor = PerformanceMonitor()
    
    try:
        monitor.start_monitoring()
        monitor.benchmark_api()
        
        print("\n📊 系统资源使用情况:")
        stats = monitor.get_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
            
    finally:
        monitor.stop_monitoring()
