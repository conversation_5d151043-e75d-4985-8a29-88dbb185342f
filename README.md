# Qwen 开源大模型服务平台

基于 Qwen 的 CPU 开源大模型服务平台，专为 Mac M2 Pro 开发，支持服务器部署。

## 功能特性

- 🚀 基于 Qwen 模型的中文对话和生成能力
- 💻 针对 Apple Silicon (M2 Pro) 优化
- 🐳 Docker 容器化部署
- 🔌 统一 REST API 接口
- 📊 支持 OA、会议、教育、邮件系统集成
- ⚡ CPU 推理优化（llama.cpp + GGUF）

## 系统要求

### Mac 开发环境
- macOS 13.0+ (Apple Silicon M1/M2)
- 内存：16GB+ 推荐
- Python 3.10+
- Git

### 服务器部署环境
- Ubuntu 20.04+ / CentOS 8+
- CPU：8核+ 推荐
- 内存：16GB+ 推荐
- Docker 20.10+

## 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <your-repo>
cd qwen-llm-platform

# 安装依赖
./scripts/setup_mac.sh
```

### 2. 模型下载
```bash
# 下载 Qwen 模型
./scripts/download_model.sh
```

### 3. 启动服务
```bash
# 本地开发
python src/main.py

# Docker 部署
docker-compose up -d
```

## 项目结构

```
qwen-llm-platform/
├── src/                    # 源代码
│   ├── main.py            # FastAPI 主服务
│   ├── models/            # 模型管理
│   ├── api/               # API 路由
│   └── integrations/      # 系统集成
├── scripts/               # 部署脚本
├── docker/                # Docker 配置
├── models/                # 模型文件存储
├── tests/                 # 测试文件
└── docs/                  # 文档
```

## API 接口

### 基础功能

#### 健康检查
```bash
curl http://localhost:8000/health
```

#### 基础对话
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "你好，请介绍一下你自己", "temperature": 0.7}'
```

#### 文档摘要
```bash
curl -X POST "http://localhost:8000/summarize" \
  -H "Content-Type: application/json" \
  -d '{"text": "长文档内容...", "max_length": 200}'
```

#### 文本翻译
```bash
curl -X POST "http://localhost:8000/translate" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello world", "target_language": "zh"}'
```

### 高级功能

#### OA 审批分析
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/oa/approval" \
  -H "Content-Type: application/json" \
  -d '{
    "document_content": "请假申请内容...",
    "approval_type": "leave"
  }'
```

#### 会议摘要
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/meeting/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "会议转录内容...",
    "meeting_type": "project_review",
    "participants": ["张三", "李四"]
  }'
```

#### 作业批改
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/education/grade" \
  -H "Content-Type: application/json" \
  -d '{
    "assignment": "解方程 2x + 3 = 11",
    "student_answer": "x = 4",
    "subject": "数学"
  }'
```

#### 邮件分类
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/email/classify" \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "紧急：系统故障",
    "content": "服务器出现问题...",
    "sender": "<EMAIL>"
  }'
```

#### 多轮对话
```bash
curl -X POST "http://localhost:8000/api/v1/chat/conversation" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "什么是AI？"},
      {"role": "assistant", "content": "AI是人工智能..."},
      {"role": "user", "content": "它有什么应用？"}
    ]
  }'
```

## 完整功能测试

### 一键测试所有功能
```bash
# 自动启动服务并运行完整测试
./test_all_features.sh
```

### 手动测试
```bash
# 1. 启动服务
./start.sh

# 2. 运行测试
python scripts/test_local.py

# 3. 运行 API 使用示例
python examples/api_usage_examples.py
```

## 系统集成示例

### OA 系统集成
- **智能审批**: 自动分析请假、采购、报销申请
- **风险评估**: 评估审批风险并提供建议
- **流程优化**: 分析审批流程效率

### 会议平台集成
- **实时转录**: 语音转文字（需配合语音识别服务）
- **会议摘要**: 自动生成会议纪要
- **行动项目**: 提取待办事项和负责人
- **决策跟踪**: 记录会议决策

### 教育平台集成
- **作业批改**: 自动批改主观题作业
- **智能答疑**: 24/7 学生问题解答
- **学习建议**: 个性化学习路径推荐
- **测验生成**: 自动生成测试题目

### 邮件系统集成
- **智能分类**: 自动分类邮件优先级
- **情感分析**: 分析邮件情感倾向
- **自动回复**: 生成专业邮件回复
- **批量处理**: 批量分析邮件内容

## 性能参考

### Mac M2 Pro (16GB)
- Qwen-1.8B-Chat: ~2-3s 响应时间
- Qwen-4B-Chat: ~4-6s 响应时间
- 并发支持: 2-3 个请求

### 服务器 (16核32GB)
- Qwen-7B-Chat: ~3-5s 响应时间
- 并发支持: 4-8 个请求

## 开发和扩展

### 添加自定义功能
```python
# 在 src/api/ 目录下创建新的路由文件
from fastapi import APIRouter

router = APIRouter(prefix="/custom", tags=["custom"])

@router.post("/my-function")
async def my_custom_function(data: str):
    # 调用 Qwen API
    # 处理业务逻辑
    return {"result": "success"}
```

### 集成到现有系统
```python
from integrations.oa_integration import OAIntegration

# 初始化集成
oa = OAIntegration()

# 分析审批文档
result = await oa.analyze_approval_document(
    document_content="审批内容",
    approval_type="leave"
)
```

## 故障排除

### 常见问题
1. **模型加载失败**: 检查模型文件是否完整下载
2. **内存不足**: 选择更小的模型或增加系统内存
3. **API 响应慢**: 调整模型参数或增加 CPU 核心数
4. **Docker 启动失败**: 检查 Docker 配置和资源限制

### 获取帮助
- 查看日志: `tail -f logs/qwen-llm.log`
- 检查服务状态: `curl http://localhost:8000/health`
- 运行诊断: `./start.sh` 选择选项 4

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- ✅ 完整的 Qwen 模型集成
- ✅ 所有 API 功能实现
- ✅ Mac M2 Pro 优化
- ✅ Docker 容器化支持
- ✅ 完整的测试套件
- ✅ 系统集成示例
