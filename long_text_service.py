#!/usr/bin/env python3
"""
长文本专用服务
"""

import os
import sys
sys.path.insert(0, 'src')

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import uvicorn

# 导入无停止词模型
from models.no_stop_qwen_model import NoStopQwenModel

app = FastAPI(title="Long Text Qwen Service")
model = None

class LongTextRequest(BaseModel):
    message: str
    max_tokens: Optional[int] = 4096
    temperature: Optional[float] = 0.9
    system_prompt: Optional[str] = None

@app.on_event("startup")
async def startup():
    global model
    model = NoStopQwenModel()
    await model.load_model()

@app.post("/long_chat")
async def long_chat(request: LongTextRequest):
    """长文本对话接口"""
    if not model or not model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")
    
    try:
        result = await model.generate(
            prompt=request.message,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            system_prompt=request.system_prompt
        )
        
        return {
            "response": result["text"],
            "model": result["model"],
            "tokens_used": result["tokens_used"],
            "processing_time": result["processing_time"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
