#!/usr/bin/env python3
"""
修复不完整响应问题
"""

import requests
import time

def test_completion_issue():
    """测试完整性问题"""
    print("🔍 测试响应完整性问题...")
    
    # 测试不同长度的请求
    test_cases = [
        {
            "name": "短回答",
            "message": "简单介绍红楼梦",
            "max_tokens": 100,
            "expected_complete": True
        },
        {
            "name": "中等回答", 
            "message": "详细介绍红楼梦的主要人物",
            "max_tokens": 300,
            "expected_complete": True
        },
        {
            "name": "长回答",
            "message": "请完整分析薛宝钗这一人物形象，结合书中具体情节详细说明她的性格特点、行为表现和在小说中的作用",
            "max_tokens": 800,
            "expected_complete": False  # 可能不完整
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print(f"请求tokens: {test_case['max_tokens']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json={
                    "message": test_case["message"],
                    "max_tokens": test_case["max_tokens"],
                    "temperature": 0.7
                },
                timeout=60  # 增加超时时间
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)
                
                # 检查是否完整
                is_complete = not (
                    response_text.endswith('的') or 
                    response_text.endswith('了') or
                    response_text.endswith('，') or
                    response_text.endswith('、') or
                    len(response_text) < 10
                )
                
                efficiency = (tokens_used / test_case['max_tokens']) * 100
                
                result = {
                    "name": test_case["name"],
                    "requested": test_case["max_tokens"],
                    "actual": tokens_used,
                    "efficiency": efficiency,
                    "time": elapsed,
                    "complete": is_complete,
                    "text_length": len(response_text),
                    "last_chars": response_text[-20:] if response_text else ""
                }
                
                results.append(result)
                
                print(f"✅ 响应成功")
                print(f"   实际tokens: {tokens_used}/{test_case['max_tokens']} ({efficiency:.1f}%)")
                print(f"   处理时间: {elapsed:.1f}s")
                print(f"   文本长度: {len(response_text)} 字符")
                print(f"   看起来完整: {'是' if is_complete else '否'}")
                print(f"   结尾: ...{response_text[-50:] if len(response_text) > 50 else response_text}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 错误: {e} (耗时: {elapsed:.1f}s)")
        
        time.sleep(2)
    
    return results

def suggest_solutions(results):
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("=" * 50)
    
    incomplete_results = [r for r in results if not r.get('complete', True)]
    
    if incomplete_results:
        print("发现不完整响应，可能原因:")
        print("1. 模型内部停止机制")
        print("2. 超时限制")
        print("3. 上下文长度限制")
        
        print("\n建议解决方案:")
        print("1. 分段请求:")
        print("   - 将长问题分解为多个短问题")
        print("   - 每个问题单独请求")
        
        print("\n2. 调整参数:")
        print("   - 降低max_tokens到500-800")
        print("   - 提高temperature到0.8增加多样性")
        print("   - 使用更明确的结束指示")
        
        print("\n3. 优化提示词:")
        print("   - 明确要求完整回答")
        print("   - 指定回答结构")
        print("   - 要求总结结尾")
    else:
        print("✅ 所有测试响应都比较完整")

def test_specific_completion():
    """测试特定的完整性"""
    print("\n🎯 测试特定完整性问题...")
    
    # 专门测试之前被截断的问题
    test_message = """请完整分析薛宝钗这一人物形象。要求：
1. 性格特点分析
2. 主要行为表现
3. 与其他人物的关系
4. 在小说中的作用和意义
请确保回答完整，以"总结"或"综上所述"结尾。"""
    
    print("测试完整性要求的提示...")
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": test_message,
                "max_tokens": 1000,
                "temperature": 0.7
            },
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            tokens_used = data.get('tokens_used', 0)
            
            print(f"✅ 响应成功")
            print(f"   Tokens: {tokens_used}/1000")
            print(f"   长度: {len(response_text)} 字符")
            
            # 检查是否有明确结尾
            has_conclusion = any(word in response_text for word in ['总结', '综上所述', '总的来说', '因此', '最后'])
            
            print(f"   有结论性结尾: {'是' if has_conclusion else '否'}")
            print(f"   完整回答:")
            print("-" * 50)
            print(response_text)
            print("-" * 50)
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    """主函数"""
    print("🔍 响应完整性诊断")
    print("=" * 60)
    
    # 1. 测试不同长度的完整性
    results = test_completion_issue()
    
    # 2. 分析结果并给出建议
    suggest_solutions(results)
    
    # 3. 测试特定的完整性问题
    test_specific_completion()
    
    print("\n🎯 快速解决方案:")
    print("如果回答经常被截断，试试这个:")
    print('curl -X POST "http://localhost:8000/chat" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -d \'{"message": "请简要分析薛宝钗，要求回答完整并以总结结尾", "max_tokens": 600, "temperature": 0.7}\'')

if __name__ == "__main__":
    main()
