
import os
import time
from typing import Optional, Dict, Any
from llama_cpp import Llama

class UltraFastQwenModel:
    """超快速 Qwen 模型"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        self.cache = {}  # 简单缓存
        
    def load_model(self, model_path: str, config: Dict[str, Any]):
        """超快速加载模型"""
        print(f"⚡ 超快速加载模型: {os.path.basename(model_path)}")
        
        start_time = time.time()
        
        self.model = Llama(
            model_path=model_path,
            
            # 核心性能参数
            n_ctx=config.get("context_length", 512),
            n_threads=config.get("threads", 8),
            n_batch=config.get("n_batch", 256),
            
            # 内存优化
            use_mmap=config.get("use_mmap", True),
            use_mlock=config.get("use_mlock", True),
            
            # 推理优化
            rope_freq_base=config.get("rope_freq_base", 10000),
            rope_freq_scale=config.get("rope_freq_scale", 1.0),
            mul_mat_q=config.get("mul_mat_q", True),
            
            # 关闭不必要的功能
            logits_all=config.get("logits_all", False),
            embedding=config.get("embedding", False),
            
            # 静默模式
            verbose=False
        )
        
        load_time = time.time() - start_time
        self.is_loaded = True
        
        print(f"✅ 模型加载完成，耗时: {load_time:.2f}s")
        
        # 预热模型
        self._warmup()
        
    def _warmup(self):
        """预热模型"""
        print("🔥 预热模型...")
        
        warmup_prompts = ["hi", "ok", "yes"]
        
        for prompt in warmup_prompts:
            try:
                self.model(prompt, max_tokens=1, temperature=0.1)
            except:
                pass
        
        print("✅ 模型预热完成")
        
    def generate_ultra_fast(self, prompt: str, max_tokens: int = 64) -> Dict[str, Any]:
        """超快速生成"""
        if not self.is_loaded:
            return {"error": "模型未加载"}
        
        # 简单缓存检查
        cache_key = f"{prompt[:50]}_{max_tokens}"
        if cache_key in self.cache:
            print("💾 使用缓存结果")
            return self.cache[cache_key]
        
        start_time = time.time()
        
        # 优化的生成参数
        result = self.model(
            prompt,
            max_tokens=min(max_tokens, 128),  # 限制最大长度
            temperature=0.1,                  # 最低随机性
            top_p=0.7,                       # 减少采样
            repeat_penalty=1.01,             # 最小重复惩罚
            stop=["\n\n", "\n", "。", ".", "!", "?", "！", "？"],  # 早停
            echo=False                       # 不回显
        )
        
        elapsed = time.time() - start_time
        
        response_data = {
            "text": result["choices"][0]["text"].strip(),
            "tokens_used": result["usage"]["total_tokens"],
            "processing_time": elapsed,
            "model": "Qwen-Ultra-Fast"
        }
        
        # 缓存结果（限制缓存大小）
        if len(self.cache) < 100:
            self.cache[cache_key] = response_data
        
        print(f"⚡ 生成完成: {elapsed:.2f}s, {result['usage']['total_tokens']} tokens")
        
        return response_data
