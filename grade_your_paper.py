#!/usr/bin/env python3
"""
批改你的数学试卷
"""

import asyncio
import sys
import os

# 添加 src 路径
sys.path.insert(0, 'src')

from integrations.education_integration import EducationIntegration

async def grade_your_math_paper():
    """批改你的数学试卷"""
    print("📝 批改数学试卷")
    print("=" * 60)
    
    edu = EducationIntegration()
    
    # 你的数学试卷（简化版，避免LaTeX问题）
    assignment_text = """
    高中数学综合试卷
    
    一、选择题（每题5分，共50分）
    1. 已知log₂a + log₂b = 1，则3^a + 9^b的最小值为（  ）
       A. 6    B. 6√3    C. 18    D. 24√3
    
    2. 直线2x-3y+1=0与直线3x+2y+4=0垂直，则直线l的方程是（  ）
       A. 3x+2y-1=0    B. 3x+2y+7=0    C. 2x-3y+5=0    D. 2x-3y+8=0
    
    3. 函数y=Asin(ωx+φ)中，A>0，ω>0，|φ|<π/2的解析式
    
    二、填空题（每题4分，共20分）
    1. 已知集合A={x|x²-3x+2=0}，B={x|x²-ax+a-1=0}，若A∪B=A，则a=______
    2. 若tanα=2，则(sinα+cosα)/(sinα-cosα)的值为______
    3. 等差数列{aₙ}的前n项和为Sₙ，若a₃+a₄+a₅=12，则S₇=______
    4. 圆C的方程为(x-1)²+(y-2)²=25，直线l被圆C截得的弦长最小值为______
    5. 分段函数f(x)，若f(a)=1/2，则a=______
    
    三、计算题（每题10分，共30分）
    1. 计算：log₂3·log₃4·log₄5·log₅6·log₆7·log₇8
    2. 已知复数z=(2+i)/(1-i)，求|z|以及z的共轭复数
    3. 求定积分∫₀¹(x²+2x)dx
    
    四、解答题（每题15分，共60分）
    1. 已知函数f(x)=x³-3x，求极值和在区间[-2,t]上最大值为2时t的范围
    2. 椭圆问题：离心率√3/2，短轴长为2，求方程和直线交点问题
    3. 线性规划：工厂生产甲乙两种产品的利润最大化问题
    4. 数列问题：Sₙ=2aₙ-2，求通项公式和前n项和
    """
    
    # 模拟学生答案
    student_answer = """
    一、选择题
    1. A (选择6)
    2. C (选择2x-3y+5=0)
    3. 需要根据图像确定，无法作答
    
    二、填空题
    1. a=2 (因为A={1,2}，要使A∪B=A，需要B⊆A)
    2. 3 (tanα=2，代入公式计算)
    3. 28 (等差数列性质，a₄=4，S₇=7×4=28)
    4. 6 (圆的半径是5，最短弦长应该是直径减去某个值)
    5. a=-1 (分段函数，需要分情况讨论)
    
    三、计算题
    1. 解：使用换底公式
       log₂3·log₃4·log₄5·log₅6·log₆7·log₇8
       = (ln3/ln2)·(ln4/ln3)·(ln5/ln4)·(ln6/ln5)·(ln7/ln6)·(ln8/ln7)
       = ln8/ln2 = ln2³/ln2 = 3ln2/ln2 = 3
    
    2. 解：复数运算
       z = (2+i)/(1-i)
       分子分母同时乘以(1+i)：
       z = (2+i)(1+i)/((1-i)(1+i)) = (2+2i+i+i²)/(1-i²) = (2+3i-1)/(1+1) = (1+3i)/2
       |z| = √((1/2)² + (3/2)²) = √(1/4 + 9/4) = √(10/4) = √10/2
       共轭复数 = (1-3i)/2
    
    3. 解：定积分计算
       ∫₀¹(x²+2x)dx = [x³/3 + x²]₀¹ = (1/3 + 1) - (0 + 0) = 4/3
    
    四、解答题
    1. f(x)=x³-3x，f'(x)=3x²-3=3(x²-1)=3(x-1)(x+1)
       极值点：x=±1
       f(-1)=(-1)³-3(-1)=-1+3=2，f(1)=1³-3(1)=1-3=-2
       所以极大值为2（x=-1），极小值为-2（x=1）
       
    2. 椭圆问题：根据离心率和短轴长可以求出a、b、c的值，然后建立方程...
    
    3. 线性规划：设甲产品x吨，乙产品y吨，目标函数为5x+3y，约束条件为...
    
    4. 数列问题：由Sₙ=2aₙ-2，可得递推关系，求出通项公式...
    """
    
    try:
        print("🔄 正在批改试卷...")
        
        result = await edu.grade_assignment(
            assignment_text=assignment_text,
            student_answer=student_answer,
            subject="数学",
            grade_level="高中"
        )
        
        if "error" in result:
            print(f"❌ 批改失败: {result['error']}")
        else:
            print("✅ 批改完成")
            print(f"科目: {result['subject']}")
            print(f"年级: {result['grade_level']}")
            print(f"处理时间: {result['processing_time']:.2f}s")
            
            print("\n📋 详细批改结果:")
            print("=" * 80)
            print(result['grading_result'])
            print("=" * 80)
            
    except Exception as e:
        print(f"❌ 批改异常: {e}")

async def grade_individual_sections():
    """分段批改"""
    print("\n📝 分段批改试卷")
    print("=" * 60)
    
    edu = EducationIntegration()
    
    sections = [
        {
            "title": "选择题部分",
            "assignment": "选择题：1. log₂a + log₂b = 1，则3^a + 9^b最小值？ 2. 直线垂直问题",
            "answer": "1. A (选择6)  2. C (选择2x-3y+5=0)"
        },
        {
            "title": "计算题：对数连乘",
            "assignment": "计算：log₂3·log₃4·log₄5·log₅6·log₆7·log₇8",
            "answer": "使用换底公式，原式=ln8/ln2=3ln2/ln2=3"
        },
        {
            "title": "计算题：复数运算",
            "assignment": "已知复数z=(2+i)/(1-i)，求|z|和共轭复数",
            "answer": "z=(1+3i)/2，|z|=√10/2，共轭复数=(1-3i)/2"
        }
    ]
    
    for i, section in enumerate(sections, 1):
        print(f"\n批改第{i}部分：{section['title']}")
        
        try:
            result = await edu.grade_assignment(
                assignment_text=section['assignment'],
                student_answer=section['answer'],
                subject="数学",
                grade_level="高中"
            )
            
            if "error" in result:
                print(f"❌ 批改失败: {result['error']}")
            else:
                print("✅ 批改结果:")
                print("-" * 50)
                print(result['grading_result'])
                print("-" * 50)
                
        except Exception as e:
            print(f"❌ 批改异常: {e}")
        
        await asyncio.sleep(1)

async def main():
    """主函数"""
    print("🎓 使用教育集成批改数学试卷")
    print("=" * 80)
    
    # 检查服务状态
    import httpx
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                print("✅ Qwen 服务运行正常")
            else:
                print(f"⚠️  Qwen 服务状态异常: {response.status_code}")
                return
    except Exception as e:
        print(f"❌ 无法连接 Qwen 服务: {e}")
        print("请确保服务正在运行: python fast_start.py")
        return
    
    # 选择批改方式
    print("\n选择批改方式:")
    print("1. 完整试卷批改")
    print("2. 分段批改")
    
    choice = input("请选择 (1 或 2，直接回车默认选择1): ").strip()
    
    if choice == "2":
        await grade_individual_sections()
    else:
        await grade_your_math_paper()
    
    print("\n🎉 批改完成！")

if __name__ == "__main__":
    asyncio.run(main())
