@use 'sass:meta';
@use 'tailwindcss';

@plugin 'daisyui' {
  themes: all;
}

html {
  scrollbar-gutter: auto;
}

.markdown {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  ul,
  ol,
  li {
    all: revert;
  }
  pre {
    @apply whitespace-pre-wrap rounded-lg p-2 mb-3;
    border: 1px solid currentColor;
  }
  p {
    @apply mb-2;
  }
  hr {
    @apply my-4 border-base-content/20 border-1;
  }
  /* TODO: fix markdown table */
}

.btn-mini {
  @apply cursor-pointer;
}
.chat-screen {
  max-width: 900px;
}

.chat-bubble {
  @apply break-words;
}

.chat-bubble-base-300 {
  --tw-bg-opacity: 1;
  --tw-text-opacity: 1;
  @apply bg-base-300 text-base-content;
}

/* Highlight.js */
[data-color-scheme='light'] {
  @include meta.load-css('highlight.js/styles/stackoverflow-light');
  .dark-color {
    @apply bg-base-content text-base-100;
  }
}
[data-color-scheme='dark'] {
  @include meta.load-css('highlight.js/styles/stackoverflow-dark');
}
[data-color-scheme='auto'] {
  @media (prefers-color-scheme: light) {
    @include meta.load-css('highlight.js/styles/stackoverflow-light');
    .dark-color {
      @apply bg-base-content text-base-100;
    }
  }
  @media (prefers-color-scheme: dark) {
    @include meta.load-css('highlight.js/styles/stackoverflow-dark');
  }
}
.hljs {
  background: transparent !important;
  padding: 0.5em !important;
}

.katex-display {
  margin: 0 0 !important;
}
