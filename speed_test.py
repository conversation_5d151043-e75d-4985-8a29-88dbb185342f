#!/usr/bin/env python3
"""
速度测试脚本
"""

import time
import requests
import json

def test_speed():
    """测试 API 响应速度"""
    
    base_url = "http://localhost:8000"
    
    test_cases = [
        {"message": "你好", "max_tokens": 10},
        {"message": "1+1=?", "max_tokens": 5},
        {"message": "今天天气", "max_tokens": 20},
        {"message": "什么是AI", "max_tokens": 50},
    ]
    
    print("🚀 开始速度测试...")
    print("=" * 50)
    
    total_time = 0
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['message']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json=test_case,
                timeout=30
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应时间: {elapsed:.2f}s")
                print(f"📝 回复: {data['response'][:50]}...")
                print(f"🔢 Token: {data.get('tokens_used', 0)}")
                
                total_time += elapsed
                success_count += 1
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 错误: {e} (耗时: {elapsed:.2f}s)")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"\n📊 测试结果:")
        print(f"   成功: {success_count}/{len(test_cases)}")
        print(f"   平均响应时间: {avg_time:.2f}s")
        print(f"   总耗时: {total_time:.2f}s")
        
        if avg_time < 3:
            print("🎉 性能优秀！")
        elif avg_time < 5:
            print("✅ 性能良好")
        else:
            print("⚠️  需要进一步优化")

if __name__ == "__main__":
    test_speed()
