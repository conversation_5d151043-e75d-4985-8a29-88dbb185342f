#!/usr/bin/env python3
"""
模型预热脚本 - 提前加载和预热模型
"""

import requests
import time

def warmup_model():
    """预热模型"""
    print("🔥 开始模型预热...")
    
    base_url = "http://localhost:8000"
    
    # 预热请求 - 使用很短的输入和输出
    warmup_requests = [
        {"message": "hi", "max_tokens": 1},
        {"message": "ok", "max_tokens": 1},
        {"message": "yes", "max_tokens": 1},
    ]
    
    for i, req in enumerate(warmup_requests, 1):
        print(f"预热 {i}/3...")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/chat",
                json=req,
                timeout=60
            )
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                print(f"✅ 预热 {i} 完成: {elapsed:.2f}s")
            else:
                print(f"❌ 预热 {i} 失败")
                
        except Exception as e:
            print(f"❌ 预热 {i} 错误: {e}")
    
    print("🎉 模型预热完成！现在响应会更快")

if __name__ == "__main__":
    warmup_model()
