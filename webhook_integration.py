
from flask import Flask, request, jsonify
import requests

app = Flask(__name__)
QWEN_API_URL = "http://localhost:8000"

@app.route("/webhook/email", methods=["POST"])
def handle_email_webhook():
    """处理邮件 Webhook"""
    
    email_data = request.json
    
    # 调用 Qwen API 分析邮件
    response = requests.post(
        f"{QWEN_API_URL}/api/v1/tasks/email/classify",
        json={
            "subject": email_data.get("subject", ""),
            "content": email_data.get("content", ""),
            "sender": email_data.get("sender", "")
        }
    )
    
    if response.status_code == 200:
        classification = response.json()
        
        # 根据分类结果采取行动
        if classification.get("priority", 0) >= 4:
            # 高优先级邮件，发送通知
            send_notification(email_data, classification)
        
        return jsonify({"status": "processed", "classification": classification})
    else:
        return jsonify({"status": "error"}), 500

def send_notification(email_data, classification):
    """发送通知"""
    # 这里可以集成钉钉、企业微信等通知系统
    print(f"高优先级邮件通知：{email_data['subject']}")

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)
