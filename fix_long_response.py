#!/usr/bin/env python3
"""
修复长文本响应问题
"""

import os
import json

def fix_long_response_config():
    """修复长文本响应配置"""
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 修复长文本响应配置...")
    print(f"当前 max_tokens: {config.get('max_tokens', 'unknown')}")
    print(f"当前 context_length: {config.get('context_length', 'unknown')}")
    
    # 调整配置以支持长文本
    config.update({
        "context_length": 2048,         # 增加上下文长度
        "max_tokens": 1024,             # 增加最大输出
        "temperature": 0.7,             # 适中的创造性
        "top_p": 0.9,                   # 增加采样范围
        "repeat_penalty": 1.05,         # 适中的重复惩罚
    })
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 长文本配置已更新")
    print(f"新的 max_tokens: {config['max_tokens']}")
    print(f"新的 context_length: {config['context_length']}")
    return True

def test_long_response():
    """测试长文本响应"""
    import requests
    import time
    
    print("\n🧪 测试长文本响应...")
    
    test_cases = [
        {
            "message": "请详细介绍人工智能的发展历史",
            "max_tokens": 500,
            "temperature": 0.7
        },
        {
            "message": "写一个关于春天的短文",
            "max_tokens": 300,
            "temperature": 0.8
        },
        {
            "message": "解释机器学习的基本概念和应用",
            "max_tokens": 400,
            "temperature": 0.6
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['message'][:20]}...")
        print(f"请求 max_tokens: {test_case['max_tokens']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json=test_case,
                timeout=30
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)
                
                print(f"✅ 响应成功")
                print(f"   实际 tokens: {tokens_used}")
                print(f"   响应长度: {len(response_text)} 字符")
                print(f"   处理时间: {elapsed:.2f}s")
                
                # 显示响应片段
                if len(response_text) > 100:
                    print(f"   响应预览: {response_text[:100]}...")
                else:
                    print(f"   完整响应: {response_text}")
                
                # 检查是否达到预期长度
                if tokens_used >= test_case['max_tokens'] * 0.8:
                    print("   🎉 长度符合预期")
                elif tokens_used >= test_case['max_tokens'] * 0.5:
                    print("   👍 长度基本合理")
                else:
                    print("   ⚠️  响应偏短")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        time.sleep(2)

def show_tips():
    """显示使用建议"""
    print("\n💡 长文本生成建议:")
    print("=" * 40)
    
    print("1. 🎯 合理设置 max_tokens:")
    print("   - 短回答: 50-200")
    print("   - 中等回答: 200-500") 
    print("   - 长文章: 500-1000+")
    
    print("\n2. 🔧 调整参数:")
    print("   - temperature: 0.7-0.8 (更有创造性)")
    print("   - top_p: 0.9 (更多样化)")
    print("   - 避免过多停止词")
    
    print("\n3. 📝 提示技巧:")
    print("   - 明确要求详细回答")
    print("   - 指定文章结构")
    print("   - 要求分段落描述")
    
    print("\n4. ⚡ 性能考虑:")
    print("   - 长文本需要更多时间")
    print("   - 可能不会命中缓存")
    print("   - 消耗更多 tokens")

if __name__ == "__main__":
    print("🔧 修复长文本响应问题")
    print("=" * 50)
    
    # 1. 修复配置
    if fix_long_response_config():
        print("\n✅ 配置修复完成")
        print("请重启服务以应用修复")
        
        # 2. 显示建议
        show_tips()
        
        print("\n🚀 重启服务后运行测试:")
        print("python fix_long_response.py")
        
        choice = input("\n是否现在测试长文本响应？(需要先重启服务) (y/N): ")
        if choice.lower() == 'y':
            test_long_response()
    else:
        print("❌ 配置修复失败")
