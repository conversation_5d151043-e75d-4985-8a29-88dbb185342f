#!/usr/bin/env python3
"""
修复空响应问题
"""

import os
import json

def fix_config():
    """修复配置文件"""
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 修复配置...")
    
    # 调整参数以避免空响应
    config.update({
        "context_length": 512,      # 增加上下文
        "max_tokens": 128,          # 增加最大输出
        "temperature": 0.5,         # 适中的随机性
        "top_p": 0.9,              # 增加采样范围
        "repeat_penalty": 1.05,     # 减少重复惩罚
    })
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置已修复")
    return True

def create_response_fix():
    """创建响应修复补丁"""
    
    fix_code = '''
# 在 lightweight_pool.py 中修复停止词设置

# 原来的停止词太严格：
# stop=["\\n\\n", "。", ".", "!", "?"]

# 修复后的停止词：
stop=["<|endoftext|>", "<|im_end|>", "\\n\\n\\n"]  # 只保留必要的停止词
'''
    
    print("🔧 修复建议:")
    print("1. 减少停止词数量")
    print("2. 增加最大 token 数")
    print("3. 调整温度参数")
    
    return fix_code

def test_fix():
    """测试修复效果"""
    import requests
    import time
    
    print("\n🧪 测试修复效果...")
    
    test_cases = [
        {"message": "hi", "max_tokens": 20},
        {"message": "what is AI", "max_tokens": 50},
        {"message": "hello world", "max_tokens": 30}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['message']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json=test_case,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                if response_text.strip():
                    print(f"✅ 有内容: {response_text[:50]}...")
                else:
                    print("❌ 仍然为空")
                    
                print(f"   Token: {data.get('tokens_used', 0)}")
                print(f"   时间: {data.get('processing_time', 0)}s")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
        
        time.sleep(1)

if __name__ == "__main__":
    print("🔧 修复空响应问题")
    print("=" * 40)
    
    # 1. 修复配置
    if fix_config():
        print("\n✅ 配置修复完成")
        print("请重启服务以应用修复")
        
        # 2. 显示修复建议
        create_response_fix()
        
        print("\n🚀 重启服务后运行测试:")
        print("python fix_empty_response.py")
        
    else:
        print("❌ 配置修复失败")
