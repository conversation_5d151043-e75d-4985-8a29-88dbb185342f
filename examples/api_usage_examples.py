#!/usr/bin/env python3
"""
Qwen LLM Platform API 使用示例
演示如何调用各种 AI 功能
"""

import asyncio
import httpx
import json
from typing import Dict, Any

class QwenAPIClient:
    """Qwen API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def basic_chat(self, message: str, temperature: float = 0.7) -> Dict[str, Any]:
        """基础对话"""
        response = await self.client.post(
            f"{self.base_url}/chat",
            json={
                "message": message,
                "temperature": temperature,
                "max_tokens": 1024
            }
        )
        return response.json()
    
    async def summarize_document(self, text: str, max_length: int = 200) -> Dict[str, Any]:
        """文档摘要"""
        response = await self.client.post(
            f"{self.base_url}/summarize",
            json={
                "text": text,
                "max_length": max_length,
                "language": "zh"
            }
        )
        return response.json()
    
    async def translate_text(self, text: str, target_language: str = "zh") -> Dict[str, Any]:
        """文本翻译"""
        response = await self.client.post(
            f"{self.base_url}/translate",
            json={
                "text": text,
                "target_language": target_language
            }
        )
        return response.json()
    
    async def analyze_approval(self, document: str, approval_type: str) -> Dict[str, Any]:
        """OA 审批分析"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/oa/approval",
            json={
                "document_content": document,
                "approval_type": approval_type
            }
        )
        return response.json()
    
    async def summarize_meeting(self, transcript: str, participants: list = None) -> Dict[str, Any]:
        """会议摘要"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/meeting/summarize",
            json={
                "transcript": transcript,
                "meeting_type": "general",
                "participants": participants or []
            }
        )
        return response.json()
    
    async def grade_homework(self, assignment: str, answer: str, subject: str) -> Dict[str, Any]:
        """作业批改"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/education/grade",
            json={
                "assignment": assignment,
                "student_answer": answer,
                "subject": subject,
                "grade_level": "高中"
            }
        )
        return response.json()
    
    async def classify_email(self, subject: str, content: str, sender: str = None) -> Dict[str, Any]:
        """邮件分类"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/email/classify",
            json={
                "subject": subject,
                "content": content,
                "sender": sender
            }
        )
        return response.json()
    
    async def generate_email_reply(self, original_email: str, reply_type: str = "formal") -> Dict[str, Any]:
        """生成邮件回复"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/tasks/email/reply",
            json={
                "original_email": original_email,
                "reply_type": reply_type,
                "key_points": ["感谢", "回复", "后续"]
            }
        )
        return response.json()
    
    async def multi_turn_conversation(self, messages: list) -> Dict[str, Any]:
        """多轮对话"""
        response = await self.client.post(
            f"{self.base_url}/api/v1/chat/conversation",
            json={
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1024
            }
        )
        return response.json()
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()

async def demo_basic_features():
    """演示基础功能"""
    print("🚀 基础功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    # 1. 基础对话
    print("\n💬 基础对话:")
    chat_result = await client.basic_chat("你好，请介绍一下你的能力")
    print(f"回复: {chat_result['response'][:100]}...")
    
    # 2. 文档摘要
    print("\n📄 文档摘要:")
    long_text = """
    人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
    这些任务包括学习、推理、问题解决、感知和语言理解。AI 技术已经在各个领域得到广泛应用，
    包括医疗保健、金融、交通、教育和娱乐。机器学习是 AI 的一个重要子领域，它使计算机能够
    从数据中学习而无需明确编程。深度学习是机器学习的一个分支，使用神经网络来模拟人脑的
    工作方式。随着计算能力的提升和大数据的可用性，AI 技术正在快速发展，为人类社会带来
    前所未有的变革。
    """
    summary_result = await client.summarize_document(long_text, max_length=100)
    print(f"摘要: {summary_result['summary']}")
    
    # 3. 文本翻译
    print("\n🌐 文本翻译:")
    translate_result = await client.translate_text("Hello, how are you today?", "zh")
    print(f"翻译: {translate_result['translated_text']}")
    
    await client.close()

async def demo_oa_features():
    """演示 OA 功能"""
    print("\n🏢 OA 系统功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    # 请假申请分析
    leave_request = """
    申请人：李明
    部门：研发部
    申请类型：病假
    请假时间：2024-01-20 至 2024-01-22
    请假天数：3天
    请假原因：感冒发烧，需要休息治疗
    医院证明：已提供
    """
    
    approval_result = await client.analyze_approval(leave_request, "leave")
    print(f"审批建议: {approval_result['recommendation']}")
    print(f"风险评分: {approval_result['risk_score']}")
    print(f"建议: {', '.join(approval_result['suggestions'][:2])}")
    
    await client.close()

async def demo_meeting_features():
    """演示会议功能"""
    print("\n🎯 会议系统功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    meeting_transcript = """
    主持人: 欢迎大家参加本次产品规划会议。
    张三: 我们的新产品开发进度如何？
    李四: 目前核心功能已完成80%，预计下月中旬可以进入测试阶段。
    王五: 市场反馈很积极，建议我们加快进度。
    张三: 好的，那我们确定下月底发布beta版本，李四负责技术，王五负责市场推广。
    李四: 明白，我会确保质量。
    王五: 我会准备推广方案。
    """
    
    meeting_result = await client.summarize_meeting(
        meeting_transcript, 
        participants=["张三", "李四", "王五"]
    )
    print(f"会议摘要: {meeting_result['summary'][:100]}...")
    print(f"关键点: {len(meeting_result['key_points'])} 个")
    print(f"行动项目: {len(meeting_result['action_items'])} 个")
    
    await client.close()

async def demo_education_features():
    """演示教育功能"""
    print("\n📚 教育系统功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    # 数学作业批改
    assignment = "解方程：2x + 5 = 13，求 x 的值"
    student_answer = "2x + 5 = 13，两边减去5得到 2x = 8，两边除以2得到 x = 4"
    
    grading_result = await client.grade_homework(assignment, student_answer, "数学")
    print(f"分数: {grading_result['score']}")
    print(f"评语: {grading_result['feedback'][:100]}...")
    print(f"建议: {', '.join(grading_result['suggestions'][:2])}")
    
    await client.close()

async def demo_email_features():
    """演示邮件功能"""
    print("\n📧 邮件系统功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    # 邮件分类
    email_subject = "紧急：系统维护通知"
    email_content = "由于系统升级需要，我们将在今晚22:00-24:00进行维护，期间服务可能中断。"
    
    classification_result = await client.classify_email(
        email_subject, 
        email_content, 
        "<EMAIL>"
    )
    print(f"邮件类别: {classification_result['category']}")
    print(f"优先级: {classification_result['priority']}")
    print(f"情感倾向: {classification_result['sentiment']}")
    
    # 邮件回复生成
    original_email = "您好，我想了解贵公司的产品价格和服务详情，请提供相关资料。"
    reply_result = await client.generate_email_reply(original_email, "formal")
    print(f"\n生成的回复: {reply_result['reply_content'][:150]}...")
    
    await client.close()

async def demo_conversation_features():
    """演示对话功能"""
    print("\n💬 多轮对话功能演示")
    print("=" * 50)
    
    client = QwenAPIClient()
    
    # 多轮对话
    messages = [
        {"role": "user", "content": "什么是机器学习？"},
        {"role": "assistant", "content": "机器学习是人工智能的一个分支，它使计算机能够从数据中学习..."},
        {"role": "user", "content": "它有哪些应用场景？"}
    ]
    
    conversation_result = await client.multi_turn_conversation(messages)
    print(f"对话ID: {conversation_result['conversation_id']}")
    print(f"回复: {conversation_result['message']['content'][:150]}...")
    print(f"处理时间: {conversation_result['processing_time']}s")
    
    await client.close()

async def main():
    """主演示函数"""
    print("🎭 Qwen LLM Platform API 使用演示")
    print("=" * 60)
    
    try:
        # 检查服务是否可用
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health")
            if response.status_code != 200:
                print("❌ 服务不可用，请先启动 Qwen LLM Platform")
                return
        
        print("✅ 服务连接成功")
        
        # 运行各种功能演示
        await demo_basic_features()
        await demo_oa_features()
        await demo_meeting_features()
        await demo_education_features()
        await demo_email_features()
        await demo_conversation_features()
        
        print("\n" + "=" * 60)
        print("🎉 所有功能演示完成！")
        print("\n💡 提示:")
        print("  - 可以修改示例中的参数来测试不同场景")
        print("  - 查看 API 文档: http://localhost:8000/docs")
        print("  - 更多集成示例请参考 src/integrations/ 目录")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("请确保 Qwen LLM Platform 服务正在运行")

if __name__ == "__main__":
    asyncio.run(main())
