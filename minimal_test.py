#!/usr/bin/env python3
"""
最小测试程序
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI(title="最小测试API")

# 静态文件
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

@app.get("/")
async def root():
    return {"message": "最小测试系统", "status": "running"}

@app.get("/test")
async def test():
    import psutil
    memory = psutil.virtual_memory()
    return {
        "memory_available_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
