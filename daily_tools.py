#!/usr/bin/env python3
"""
日常实用工具
基于 Qwen LLM Platform 的实用小工具
"""

import requests
import argparse
import sys

class DailyTools:
    """日常工具集"""
    
    def __init__(self, api_url="http://localhost:8000"):
        self.api_url = api_url
    
    def smart_summary(self, text_file):
        """智能文档摘要"""
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            response = requests.post(
                f"{self.api_url}/summarize",
                json={"text": content, "max_length": 200}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("📄 文档摘要：")
                print(result["summary"])
                print(f"\n原文长度：{result['original_length']} 字符")
            else:
                print("❌ 摘要生成失败")
                
        except Exception as e:
            print(f"❌ 错误：{e}")
    
    def quick_translate(self, text, target_lang="zh"):
        """快速翻译"""
        response = requests.post(
            f"{self.api_url}/translate",
            json={"text": text, "target_language": target_lang}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"🌐 翻译结果：{result['translated_text']}")
        else:
            print("❌ 翻译失败")
    
    def ai_chat(self, message):
        """AI 对话"""
        response = requests.post(
            f"{self.api_url}/chat",
            json={"message": message, "max_tokens": 200}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"🤖 AI 回复：{result['response']}")
        else:
            print("❌ 对话失败")
    
    def email_helper(self, email_file):
        """邮件助手"""
        try:
            with open(email_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            subject = ""
            content = ""
            
            for line in lines:
                if line.startswith("Subject:"):
                    subject = line.replace("Subject:", "").strip()
                else:
                    content += line
            
            # 分类邮件
            response = requests.post(
                f"{self.api_url}/api/v1/tasks/email/classify",
                json={
                    "subject": subject,
                    "content": content.strip()
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"📧 邮件分析：")
                print(f"   类别：{result['category']}")
                print(f"   优先级：{result['priority']}/5")
                print(f"   情感：{result['sentiment']}")
                print(f"   建议：{result['suggested_response']}")
            else:
                print("❌ 邮件分析失败")
                
        except Exception as e:
            print(f"❌ 错误：{e}")

def main():
    """命令行工具主函数"""
    
    parser = argparse.ArgumentParser(description="Qwen LLM 日常工具")
    parser.add_argument("command", choices=["summary", "translate", "chat", "email"], 
                       help="选择功能")
    parser.add_argument("--file", "-f", help="输入文件路径")
    parser.add_argument("--text", "-t", help="输入文本")
    parser.add_argument("--lang", "-l", default="zh", help="目标语言")
    
    args = parser.parse_args()
    
    tools = DailyTools()
    
    if args.command == "summary":
        if not args.file:
            print("❌ 请指定文件路径：--file <文件路径>")
            return
        tools.smart_summary(args.file)
    
    elif args.command == "translate":
        if not args.text:
            print("❌ 请指定要翻译的文本：--text <文本>")
            return
        tools.quick_translate(args.text, args.lang)
    
    elif args.command == "chat":
        if not args.text:
            print("❌ 请指定对话内容：--text <消息>")
            return
        tools.ai_chat(args.text)
    
    elif args.command == "email":
        if not args.file:
            print("❌ 请指定邮件文件路径：--file <邮件文件>")
            return
        tools.email_helper(args.file)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🛠️  Qwen LLM 日常工具")
        print("=" * 40)
        print("使用示例：")
        print("  文档摘要：python daily_tools.py summary --file document.txt")
        print("  快速翻译：python daily_tools.py translate --text 'Hello world' --lang zh")
        print("  AI 对话：python daily_tools.py chat --text '什么是人工智能？'")
        print("  邮件分析：python daily_tools.py email --file email.txt")
    else:
        main()
