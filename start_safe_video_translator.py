#!/usr/bin/env python3
"""
安全启动视频翻译系统
避免内存问题和模型崩溃
"""

import os
import sys
import subprocess
import psutil
import gc
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_system_resources():
    """检查系统资源"""
    print("🔍 检查系统资源...")
    
    # 检查内存
    memory = psutil.virtual_memory()
    available_gb = memory.available / (1024**3)
    
    print(f"💾 可用内存: {available_gb:.1f} GB")
    
    if available_gb < 2.0:
        print("⚠️  警告: 可用内存不足2GB，可能影响性能")
        return False
    
    # 检查磁盘空间
    disk = psutil.disk_usage('.')
    free_gb = disk.free / (1024**3)
    
    print(f"💿 可用磁盘: {free_gb:.1f} GB")
    
    if free_gb < 1.0:
        print("⚠️  警告: 可用磁盘空间不足1GB")
        return False
    
    return True

def set_memory_limits():
    """设置内存限制"""
    print("🔧 设置内存优化...")
    
    # 设置环境变量限制内存使用
    os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'  # 禁用MPS高水位
    os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'  # 启用MPS回退
    os.environ['OMP_NUM_THREADS'] = '2'  # 限制OpenMP线程
    os.environ['MKL_NUM_THREADS'] = '2'  # 限制MKL线程
    
    # 强制垃圾回收
    gc.collect()
    
    print("✅ 内存优化设置完成")

def kill_existing_processes():
    """杀死可能存在的Python进程"""
    print("🔄 检查现有进程...")
    
    current_pid = os.getpid()
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] == 'Python' and proc.info['pid'] != current_pid:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'main.py' in cmdline or 'uvicorn' in cmdline:
                    print(f"🔪 终止进程: {proc.info['pid']} - {cmdline}")
                    proc.terminate()
                    killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if killed_count > 0:
        print(f"✅ 终止了 {killed_count} 个进程")
    else:
        print("✅ 没有发现需要终止的进程")

def create_safe_main():
    """创建安全的主程序"""
    safe_main_content = '''#!/usr/bin/env python3
"""
安全的主程序 - 避免内存问题
"""

import os
import sys
import gc
import logging
from datetime import datetime
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn

# 设置内存优化
os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
os.environ['OMP_NUM_THREADS'] = '2'
os.environ['MKL_NUM_THREADS'] = '2'

# 强制垃圾回收
gc.collect()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="安全视频翻译API", version="1.0.0")

# 静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    return {"message": "安全视频翻译系统", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    import psutil
    
    memory = psutil.virtual_memory()
    return {
        "status": "healthy",
        "memory_available_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/translate_video_safe")
async def translate_video_safe(
    file: UploadFile = File(...),
    target_language: str = Form("zh"),
    source_language: str = Form("auto"),
    force_transcribe: bool = Form(False)
):
    """安全的视频翻译接口"""
    
    # 验证文件
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    file_ext = file.filename.lower().split('.')[-1]
    supported_formats = ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'wmv']
    
    if file_ext not in supported_formats:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的视频格式。支持: {', '.join(supported_formats)}"
        )
    
    try:
        # 导入轻量级翻译器
        from integrations.lightweight_video_translator import LightweightVideoTranslator
        from integrations.stable_video_processor import SimpleVideoUploadHandler
        
        # 保存上传文件
        upload_handler = SimpleVideoUploadHandler()
        file_content = await file.read()
        video_path = await upload_handler.save_uploaded_file(file_content, file.filename)
        
        # 创建轻量级翻译器
        translator = LightweightVideoTranslator()
        
        # 检查依赖
        deps = translator.check_dependencies()
        
        missing_deps = []
        if not deps['ffmpeg']:
            missing_deps.append("ffmpeg")
        if not deps['whisper'] and force_transcribe:
            missing_deps.append("whisper")
        
        if missing_deps:
            raise HTTPException(
                status_code=500, 
                detail=f"缺少依赖: {', '.join(missing_deps)}"
            )
        
        # 处理视频
        result = await translator.process_video_lightweight(
            video_path, 
            target_language, 
            source_language,
            force_transcribe
        )
        
        # 清理
        translator.cleanup()
        
        # 强制垃圾回收
        gc.collect()
        
        if result.get("success"):
            return {
                "message": "视频翻译完成",
                "video_info": {
                    "filename": result["video_file"],
                    "size_mb": result["file_size_mb"],
                    "subtitle_source": result.get("subtitle_source", "unknown")
                },
                "translation_info": {
                    "source_language": result.get("detected_language", source_language),
                    "target_language": target_language
                },
                "processing_steps": result["processing_steps"],
                "subtitles": {
                    "original": result.get("original_subtitles", ""),
                    "translated": result.get("translated_subtitles", "")
                },
                "processed_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "视频翻译失败"))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频翻译错误: {e}")
        # 强制垃圾回收
        gc.collect()
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """主函数"""
    # 从环境变量读取配置
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    
    logger.info(f"🌐 启动安全服务: http://{host}:{port}")
    logger.info(f"📖 API 文档: http://{host}:{port}/docs")
    logger.info(f"🎬 视频翻译: http://{host}:{port}/static/video_translator.html")
    
    uvicorn.run(
        "safe_main:app",
        host=host,
        port=port,
        reload=False,  # 禁用重载避免内存问题
        log_level="info",
        workers=1  # 单进程避免内存问题
    )

if __name__ == "__main__":
    main()
'''
    
    with open("src/safe_main.py", "w", encoding="utf-8") as f:
        f.write(safe_main_content)
    
    print("✅ 创建安全主程序: src/safe_main.py")

def main():
    """主函数"""
    print("🛡️  安全视频翻译系统启动器")
    print("=" * 50)
    
    # 检查系统资源
    if not check_system_resources():
        print("❌ 系统资源不足，建议关闭其他应用程序")
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # 设置内存限制
    set_memory_limits()
    
    # 杀死现有进程
    kill_existing_processes()
    
    # 创建安全主程序
    create_safe_main()
    
    print("\n🚀 启动安全服务...")
    
    try:
        # 切换到src目录并启动
        os.chdir("src")
        subprocess.run([sys.executable, "safe_main.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
