# Qwen LLM Platform 部署指南

## 概述

本指南详细介绍如何在 Mac M2 Pro 和 Linux 服务器上部署 Qwen 开源大模型服务平台。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端应用     │    │   Nginx 代理     │    │  FastAPI 服务   │
│  (OA/会议/教育)  │◄──►│   (负载均衡)     │◄──►│   (API 接口)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │  Qwen 模型引擎   │
                                               │  (llama.cpp)    │
                                               └─────────────────┘
```

## Mac M2 Pro 开发环境

### 1. 快速开始

```bash
# 克隆项目
git clone <your-repo>
cd qwen-llm-platform

# 一键启动
./start.sh
```

### 2. 手动安装

```bash
# 1. 安装环境
./scripts/setup_mac.sh

# 2. 下载模型
./scripts/download_model.sh

# 3. 启动服务
source venv/bin/activate
cd src
python main.py
```

### 3. 性能优化

#### Apple Silicon 优化
- 使用 Metal 加速：编译时启用 `LLAMA_METAL=1`
- 内存管理：根据可用内存选择合适的模型大小
- 线程配置：设置为 CPU 核心数

#### 模型选择建议
| 内存大小 | 推荐模型 | 响应时间 | 并发能力 |
|---------|---------|---------|---------|
| 8GB     | Qwen-1.8B | 2-3s    | 1-2     |
| 16GB    | Qwen-4B   | 4-6s    | 2-3     |
| 32GB+   | Qwen-7B   | 6-8s    | 3-4     |

## Linux 服务器部署

### 1. 自动部署

```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/scripts/deploy_server.sh
chmod +x deploy_server.sh

# 运行部署
sudo ./deploy_server.sh
```

### 2. Docker 部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps
```

### 3. 生产环境配置

#### 系统要求
- **CPU**: 8核+ (推荐16核)
- **内存**: 16GB+ (推荐32GB)
- **存储**: 50GB+ SSD
- **网络**: 1Gbps+

#### 性能调优
```bash
# 1. 系统优化
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.rmem_max=134217728' >> /etc/sysctl.conf

# 2. 进程限制
ulimit -n 65536
ulimit -u 32768

# 3. 内存优化
echo 'never' > /sys/kernel/mm/transparent_hugepage/enabled
```

## API 接口文档

### 基础接口

#### 健康检查
```bash
curl http://localhost:8000/health
```

#### 对话接口
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下你自己",
    "temperature": 0.7,
    "max_tokens": 200
  }'
```

#### 文档摘要
```bash
curl -X POST "http://localhost:8000/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "长文档内容...",
    "max_length": 200,
    "language": "zh"
  }'
```

### 系统集成接口

#### OA 审批分析
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/oa/approval" \
  -H "Content-Type: application/json" \
  -d '{
    "document_content": "请假申请内容...",
    "approval_type": "leave"
  }'
```

#### 会议摘要
```bash
curl -X POST "http://localhost:8000/api/v1/tasks/meeting/summarize" \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "会议转录内容...",
    "meeting_type": "project_review"
  }'
```

## 系统集成示例

### 1. OA 系统集成

```python
from integrations.oa_integration import OAIntegration

# 初始化
oa = OAIntegration()

# 分析审批文档
result = await oa.analyze_approval_document(
    document_content="请假申请内容",
    approval_type="请假申请"
)
```

### 2. 会议系统集成

```python
from integrations.meeting_integration import MeetingIntegration

# 初始化
meeting = MeetingIntegration()

# 会议摘要
summary = await meeting.summarize_meeting_transcript(
    transcript="会议转录内容",
    meeting_info={"title": "项目讨论会"}
)
```

### 3. 教育系统集成

```python
from integrations.education_integration import EducationIntegration

# 初始化
edu = EducationIntegration()

# 作业批改
grading = await edu.grade_assignment(
    assignment_text="题目内容",
    student_answer="学生答案",
    subject="数学"
)
```

### 4. 邮件系统集成

```python
from integrations.email_integration import EmailIntegration

# 初始化
email_ai = EmailIntegration()

# 邮件分类
classification = await email_ai.classify_email(
    subject="邮件主题",
    content="邮件内容",
    sender="发件人"
)
```

## 监控和维护

### 1. 日志管理

```bash
# 查看应用日志
tail -f logs/qwen-llm.log

# 查看 Docker 日志
docker-compose logs -f qwen-llm
```

### 2. 性能监控

```bash
# 系统资源监控
htop

# API 性能测试
python scripts/test_local.py
```

### 3. 备份策略

```bash
# 备份模型和配置
tar -czf backup-$(date +%Y%m%d).tar.gz models/ .env

# 数据库备份（如果使用）
pg_dump qwen_db > backup-db-$(date +%Y%m%d).sql
```

## 故障排除

### 常见问题

#### 1. 模型加载失败
```bash
# 检查模型文件
ls -la models/
cat models/model_config.json

# 重新下载模型
./scripts/download_model.sh
```

#### 2. 内存不足
```bash
# 检查内存使用
free -h

# 选择更小的模型
# 编辑 models/model_config.json
```

#### 3. API 响应慢
```bash
# 检查 CPU 使用率
top

# 调整线程数
# 编辑 models/model_config.json 中的 threads 参数
```

#### 4. Docker 容器启动失败
```bash
# 查看详细日志
docker-compose logs qwen-llm

# 重新构建镜像
docker-compose build --no-cache
```

### 性能基准

| 环境 | 模型 | 响应时间 | 吞吐量 | 内存使用 |
|------|------|---------|--------|---------|
| Mac M2 Pro 16GB | Qwen-4B | 4-6s | 10 req/min | 8GB |
| Linux 16核32GB | Qwen-7B | 3-5s | 20 req/min | 12GB |
| Docker 容器 | Qwen-4B | 5-7s | 15 req/min | 10GB |

## 安全配置

### 1. API 安全
```bash
# 设置 API 密钥
export API_KEY="your-secret-key"

# 启用 HTTPS
# 配置 SSL 证书
```

### 2. 防火墙配置
```bash
# Ubuntu/Debian
ufw allow 8000/tcp
ufw enable

# CentOS/RHEL
firewall-cmd --permanent --add-port=8000/tcp
firewall-cmd --reload
```

### 3. 访问控制
```nginx
# Nginx 配置示例
location /api/ {
    allow 192.168.1.0/24;
    deny all;
    proxy_pass http://qwen_backend;
}
```

## 扩展开发

### 1. 添加新的集成
```python
# 创建新的集成类
class CustomIntegration:
    def __init__(self, qwen_api_url: str):
        self.qwen_api_url = qwen_api_url
    
    async def custom_function(self, data: str):
        # 实现自定义功能
        pass
```

### 2. 自定义 API 路由
```python
# 在 src/api/ 目录下创建新的路由文件
from fastapi import APIRouter

router = APIRouter(prefix="/custom", tags=["custom"])

@router.post("/endpoint")
async def custom_endpoint():
    # 实现自定义端点
    pass
```

## 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查系统资源
3. 参考故障排除指南
4. 提交 Issue 到项目仓库
