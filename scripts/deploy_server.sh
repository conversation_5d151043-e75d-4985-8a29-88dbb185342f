#!/bin/bash

# Qwen LLM Platform 服务器部署脚本
# 适用于 Ubuntu 20.04+ / CentOS 8+

set -e

echo "🚀 开始部署 Qwen LLM Platform 到服务器..."

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "❌ 无法检测操作系统"
    exit 1
fi

echo "📱 检测到操作系统: $OS $VER"

# 1. 更新系统包
echo "📦 更新系统包..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git build-essential cmake python3 python3-pip python3-venv
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    sudo yum update -y
    sudo yum groupinstall -y "Development Tools"
    sudo yum install -y curl wget git cmake python3 python3-pip
else
    echo "❌ 不支持的操作系统: $OS"
    exit 1
fi

# 2. 安装 Docker
echo "🐳 安装 Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    
    # 安装 Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    echo "✅ Docker 安装完成"
else
    echo "✅ Docker 已安装"
fi

# 3. 创建项目目录
echo "📁 创建项目目录..."
PROJECT_DIR="/opt/qwen-llm-platform"
sudo mkdir -p $PROJECT_DIR
sudo chown $USER:$USER $PROJECT_DIR
cd $PROJECT_DIR

# 4. 克隆或复制项目文件
echo "📥 部署项目文件..."
# 如果是从 Git 仓库部署
# git clone <your-repo-url> .

# 或者从本地复制文件（需要先上传到服务器）
# 这里假设文件已经上传到服务器

# 5. 创建必要目录
mkdir -p models logs data ssl

# 6. 设置环境变量
echo "🔧 配置环境变量..."
cat > .env << EOF
# Qwen LLM Platform 生产环境配置
MODEL_PATH=/opt/qwen-llm-platform/models
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
MAX_TOKENS=2048
TEMPERATURE=0.7

# 数据库配置 (可选)
# DATABASE_URL=postgresql://user:pass@localhost/qwen_db

# Redis 配置 (可选)
REDIS_URL=redis://localhost:6379

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)
EOF

# 7. 下载模型
echo "🤖 下载模型..."
if [ ! -f "models/qwen1_5-4b-chat-q4_k_m.gguf" ]; then
    echo "📥 下载 Qwen-1.5-4B-Chat 模型..."
    wget -c -O "models/qwen1_5-4b-chat-q4_k_m.gguf" \
        "https://huggingface.co/Qwen/Qwen1.5-4B-Chat-GGUF/resolve/main/qwen1_5-4b-chat-q4_k_m.gguf"
fi

# 8. 创建模型配置
cat > models/model_config.json << EOF
{
    "model_name": "Qwen-1.5-4B-Chat",
    "model_file": "qwen1_5-4b-chat-q4_k_m.gguf",
    "model_path": "models/qwen1_5-4b-chat-q4_k_m.gguf",
    "context_length": 32768,
    "max_tokens": 2048,
    "temperature": 0.7,
    "top_p": 0.8,
    "repeat_penalty": 1.1,
    "threads": $(nproc),
    "gpu_layers": 0
}
EOF

# 9. 创建 Nginx 配置
echo "🌐 配置 Nginx..."
cat > nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream qwen_backend {
        server qwen-llm:8000;
    }

    server {
        listen 80;
        server_name _;

        location / {
            proxy_pass http://qwen_backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            
            # 增加超时时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
EOF

# 10. 创建 systemd 服务文件
echo "⚙️  创建系统服务..."
sudo tee /etc/systemd/system/qwen-llm.service > /dev/null << EOF
[Unit]
Description=Qwen LLM Platform
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$PROJECT_DIR
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# 11. 启用并启动服务
echo "🚀 启动服务..."
sudo systemctl daemon-reload
sudo systemctl enable qwen-llm.service

# 12. 构建并启动 Docker 容器
echo "🐳 构建 Docker 镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 13. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 14. 测试服务
echo "🧪 测试服务..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 API 地址: http://$(hostname -I | awk '{print $1}'):8000"
    echo "📖 API 文档: http://$(hostname -I | awk '{print $1}'):8000/docs"
else
    echo "❌ 服务启动失败，请检查日志:"
    docker-compose logs qwen-llm
    exit 1
fi

# 15. 设置防火墙规则
echo "🔥 配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow 80/tcp
    sudo ufw allow 8000/tcp
    echo "✅ UFW 防火墙规则已添加"
elif command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --add-port=80/tcp
    sudo firewall-cmd --permanent --add-port=8000/tcp
    sudo firewall-cmd --reload
    echo "✅ Firewalld 防火墙规则已添加"
fi

echo ""
echo "🎉 Qwen LLM Platform 部署完成！"
echo ""
echo "📊 服务状态:"
echo "   - API 服务: http://$(hostname -I | awk '{print $1}'):8000"
echo "   - 健康检查: http://$(hostname -I | awk '{print $1}'):8000/health"
echo "   - API 文档: http://$(hostname -I | awk '{print $1}'):8000/docs"
echo ""
echo "🔧 管理命令:"
echo "   - 查看状态: docker-compose ps"
echo "   - 查看日志: docker-compose logs -f"
echo "   - 重启服务: docker-compose restart"
echo "   - 停止服务: docker-compose down"
echo ""
echo "📁 项目目录: $PROJECT_DIR"
