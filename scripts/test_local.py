#!/usr/bin/env python3
"""
本地测试脚本
测试 Qwen 模型的性能和功能
"""

import asyncio
import time
import json
import httpx
from typing import Dict, Any

class QwenTester:
    """Qwen 模型测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = await self.client.get(f"{self.base_url}/health")
            result = response.json()
            print(f"✅ 健康检查: {result['status']}")
            print(f"📊 系统信息: {result['system_info']}")
            return result
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return {}
    
    async def test_basic_chat(self) -> Dict[str, Any]:
        """测试基础对话"""
        print("\n💬 测试基础对话...")
        
        test_messages = [
            "你好，请介绍一下你自己",
            "请用中文写一首关于春天的诗",
            "解释一下什么是人工智能",
            "1+1等于几？请详细解释",
        ]
        
        results = []
        
        for message in test_messages:
            print(f"🤖 测试消息: {message}")
            start_time = time.time()
            
            try:
                response = await self.client.post(
                    f"{self.base_url}/chat",
                    json={
                        "message": message,
                        "temperature": 0.7,
                        "max_tokens": 200
                    }
                )
                
                result = response.json()
                elapsed = time.time() - start_time
                
                print(f"📝 回复: {result['response'][:100]}...")
                print(f"⏱️  响应时间: {elapsed:.2f}s")
                print(f"🔢 Token 使用: {result['tokens_used']}")
                print("-" * 50)
                
                results.append({
                    "message": message,
                    "response": result['response'],
                    "response_time": elapsed,
                    "tokens_used": result['tokens_used']
                })
                
            except Exception as e:
                print(f"❌ 对话测试失败: {e}")
                results.append({
                    "message": message,
                    "error": str(e)
                })
        
        return {"chat_results": results}
    
    async def test_summarize(self) -> Dict[str, Any]:
        """测试文档摘要"""
        print("\n📄 测试文档摘要...")
        
        test_text = """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
        人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。
        可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
        人工智能可以对人的意识、思维的信息过程的模拟。
        人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
        """
        
        try:
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/summarize",
                json={
                    "text": test_text,
                    "max_length": 100,
                    "language": "zh"
                }
            )
            
            result = response.json()
            elapsed = time.time() - start_time
            
            print(f"📝 原文长度: {result['original_length']} 字符")
            print(f"📄 摘要: {result['summary']}")
            print(f"⏱️  响应时间: {elapsed:.2f}s")
            
            return {
                "original_length": result['original_length'],
                "summary": result['summary'],
                "response_time": elapsed
            }
            
        except Exception as e:
            print(f"❌ 摘要测试失败: {e}")
            return {"error": str(e)}
    
    async def test_translate(self) -> Dict[str, Any]:
        """测试翻译功能"""
        print("\n🌐 测试翻译功能...")
        
        test_cases = [
            {"text": "Hello, how are you?", "target": "中文"},
            {"text": "你好，今天天气怎么样？", "target": "English"},
        ]
        
        results = []
        
        for case in test_cases:
            print(f"🔤 翻译: {case['text']} -> {case['target']}")
            
            try:
                start_time = time.time()
                response = await self.client.post(
                    f"{self.base_url}/translate",
                    json={
                        "text": case['text'],
                        "target_language": case['target']
                    }
                )
                
                result = response.json()
                elapsed = time.time() - start_time
                
                print(f"📝 翻译结果: {result['translated_text']}")
                print(f"⏱️  响应时间: {elapsed:.2f}s")
                print("-" * 30)
                
                results.append({
                    "original": case['text'],
                    "translated": result['translated_text'],
                    "response_time": elapsed
                })
                
            except Exception as e:
                print(f"❌ 翻译测试失败: {e}")
                results.append({
                    "original": case['text'],
                    "error": str(e)
                })
        
        return {"translate_results": results}
    
    async def performance_test(self, concurrent_requests: int = 3) -> Dict[str, Any]:
        """性能压力测试"""
        print(f"\n⚡ 性能测试 (并发: {concurrent_requests})")
        
        async def single_request():
            start_time = time.time()
            try:
                response = await self.client.post(
                    f"{self.base_url}/chat",
                    json={
                        "message": "请简单介绍一下机器学习",
                        "max_tokens": 100
                    }
                )
                elapsed = time.time() - start_time
                return {"success": True, "time": elapsed}
            except Exception as e:
                elapsed = time.time() - start_time
                return {"success": False, "time": elapsed, "error": str(e)}
        
        # 并发请求
        tasks = [single_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        
        # 统计结果
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]

        avg_time = 0.0
        max_time = 0.0
        min_time = 0.0

        if successful:
            avg_time = sum(r["time"] for r in successful) / len(successful)
            max_time = max(r["time"] for r in successful)
            min_time = min(r["time"] for r in successful)

            print(f"✅ 成功请求: {len(successful)}/{concurrent_requests}")
            print(f"⏱️  平均响应时间: {avg_time:.2f}s")
            print(f"📊 最快/最慢: {min_time:.2f}s / {max_time:.2f}s")

        if failed:
            print(f"❌ 失败请求: {len(failed)}")
            for f in failed:
                print(f"   错误: {f.get('error', 'Unknown')}")

        return {
            "total_requests": concurrent_requests,
            "successful": len(successful),
            "failed": len(failed),
            "avg_response_time": avg_time,
            "max_response_time": max_time,
            "min_response_time": min_time
        }
    
    async def test_oa_approval(self) -> Dict[str, Any]:
        """测试 OA 审批分析"""
        print("\n📋 测试 OA 审批分析...")

        test_cases = [
            {
                "document_content": """
                申请人：张三
                部门：技术部
                申请类型：年假
                请假时间：2024-01-15 至 2024-01-20
                请假天数：5天
                请假原因：家庭旅行，已提前安排工作交接
                """,
                "approval_type": "leave"
            },
            {
                "document_content": """
                申请部门：市场部
                采购物品：办公电脑 10台
                预算金额：50000元
                采购理由：部门扩张，新员工入职需要
                供应商：联想官方授权经销商
                """,
                "approval_type": "purchase"
            }
        ]

        results = []

        for case in test_cases:
            print(f"🔍 测试 {case['approval_type']} 审批...")

            try:
                response = await self.client.post(
                    f"{self.base_url}/api/v1/tasks/oa/approval",
                    json=case
                )

                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 审批建议: {result['recommendation']}")
                    print(f"📊 风险评分: {result['risk_score']}")
                    print(f"💡 建议: {', '.join(result['suggestions'][:2])}")
                    results.append(result)
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    results.append({"error": f"HTTP {response.status_code}"})

            except Exception as e:
                print(f"❌ 测试失败: {e}")
                results.append({"error": str(e)})

        return {"oa_approval_results": results}

    async def test_meeting_summary(self) -> Dict[str, Any]:
        """测试会议摘要"""
        print("\n🎯 测试会议摘要...")

        test_transcript = """
        张三: 大家好，今天我们讨论新产品的发布计划。目前开发进度如何？
        李四: 核心功能已经完成90%，预计下周可以进入测试阶段。
        王五: 市场调研显示，用户对这类产品需求很大，建议尽快发布。
        张三: 那我们确定发布时间为下个月15号，李四负责技术测试，王五负责市场推广准备。
        李四: 好的，我会在下周五前完成所有测试。
        王五: 我这边会准备推广方案，下周三给大家看初稿。
        张三: 很好，那我们下周五再开会确认最终方案。
        """

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/tasks/meeting/summarize",
                json={
                    "transcript": test_transcript,
                    "meeting_type": "product_planning",
                    "participants": ["张三", "李四", "王五"]
                }
            )

            if response.status_code == 200:
                result = response.json()
                print(f"📝 会议摘要: {result['summary'][:100]}...")
                print(f"🔑 关键点数量: {len(result['key_points'])}")
                print(f"📋 行动项目: {len(result['action_items'])}")
                print(f"✅ 决策事项: {len(result['decisions'])}")
                return result
            else:
                print(f"❌ 请求失败: {response.status_code}")
                return {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return {"error": str(e)}

    async def test_education_grading(self) -> Dict[str, Any]:
        """测试教育批改"""
        print("\n📚 测试教育批改...")

        test_cases = [
            {
                "assignment": "请解释牛顿第一定律的含义和应用",
                "student_answer": "牛顿第一定律说物体在没有外力作用时会保持静止或匀速直线运动。比如在太空中的物体会一直飞行，除非有其他力作用。",
                "subject": "物理",
                "grade_level": "高中"
            },
            {
                "assignment": "计算 2x + 3 = 11 中 x 的值",
                "student_answer": "2x + 3 = 11，所以 2x = 11 - 3 = 8，因此 x = 4",
                "subject": "数学",
                "grade_level": "初中"
            }
        ]

        results = []

        for case in test_cases:
            print(f"📖 测试 {case['subject']} 作业批改...")

            try:
                response = await self.client.post(
                    f"{self.base_url}/api/v1/tasks/education/grade",
                    json=case
                )

                if response.status_code == 200:
                    result = response.json()
                    print(f"📊 分数: {result['score']}")
                    print(f"💬 评语: {result['feedback'][:80]}...")
                    print(f"💡 建议数量: {len(result['suggestions'])}")
                    results.append(result)
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    results.append({"error": f"HTTP {response.status_code}"})

            except Exception as e:
                print(f"❌ 测试失败: {e}")
                results.append({"error": str(e)})

        return {"education_results": results}

    async def test_email_functions(self) -> Dict[str, Any]:
        """测试邮件功能"""
        print("\n📧 测试邮件功能...")

        # 测试邮件分类
        print("🔍 测试邮件分类...")
        classification_test = {
            "subject": "紧急：服务器故障需要立即处理",
            "content": "我们的主服务器出现故障，网站无法访问，请技术团队立即处理。",
            "sender": "<EMAIL>"
        }

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/tasks/email/classify",
                json=classification_test
            )

            classification_result = {}
            if response.status_code == 200:
                classification_result = response.json()
                print(f"📂 邮件类别: {classification_result['category']}")
                print(f"⭐ 优先级: {classification_result['priority']}")
                print(f"😊 情感倾向: {classification_result['sentiment']}")
            else:
                classification_result = {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            classification_result = {"error": str(e)}

        # 测试邮件回复生成
        print("✍️ 测试邮件回复生成...")
        reply_test = {
            "original_email": "您好，请问贵公司的产品价格和技术支持服务如何？我们正在寻找合适的解决方案。",
            "reply_type": "formal",
            "key_points": ["产品价格", "技术支持", "解决方案"]
        }

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/tasks/email/reply",
                json=reply_test
            )

            reply_result = {}
            if response.status_code == 200:
                reply_result = response.json()
                print(f"📝 回复内容: {reply_result['reply_content'][:100]}...")
                print(f"🎭 语调: {reply_result['tone']}")
                print(f"💡 建议数量: {len(reply_result['suggestions'])}")
            else:
                reply_result = {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            reply_result = {"error": str(e)}

        return {
            "email_classification": classification_result,
            "email_reply": reply_result
        }

    async def test_conversation_api(self) -> Dict[str, Any]:
        """测试对话 API"""
        print("\n💬 测试对话 API...")

        # 测试多轮对话
        messages = [
            {"role": "user", "content": "你好，我想了解人工智能"},
            {"role": "assistant", "content": "您好！我很乐意为您介绍人工智能。人工智能是计算机科学的一个分支..."},
            {"role": "user", "content": "AI 在教育领域有什么应用？"}
        ]

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/chat/conversation",
                json={
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 500
                }
            )

            if response.status_code == 200:
                result = response.json()
                conversation_id = result["conversation_id"]
                print(f"💬 对话ID: {conversation_id}")
                print(f"📝 回复: {result['message']['content'][:100]}...")
                print(f"⏱️ 处理时间: {result['processing_time']}s")

                # 测试获取对话历史
                print("📚 测试获取对话历史...")
                history_count = 0
                try:
                    history_response = await self.client.get(f"{self.base_url}/api/v1/chat/conversations")

                    if history_response.status_code == 200:
                        history_result = history_response.json()
                        history_count = history_result.get('total', 0)
                        print(f"📋 对话总数: {history_count}")
                except Exception as e:
                    print(f"⚠️ 获取对话历史失败: {e}")

                return {
                    "conversation_result": result,
                    "conversation_id": conversation_id,
                    "history_count": history_count
                }
            else:
                return {"error": f"HTTP {response.status_code}"}

        except Exception as e:
            print(f"❌ 对话测试失败: {e}")
            return {"error": str(e)}

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 Qwen LLM Platform 完整功能测试")
        print("=" * 60)

        # 等待服务启动
        print("⏳ 等待服务启动...")
        await asyncio.sleep(2)

        test_results = {}

        # 健康检查
        test_results["health"] = await self.test_health()

        # 基础功能测试
        test_results["chat"] = await self.test_basic_chat()
        test_results["summarize"] = await self.test_summarize()
        test_results["translate"] = await self.test_translate()

        # 高级功能测试
        test_results["oa_approval"] = await self.test_oa_approval()
        test_results["meeting_summary"] = await self.test_meeting_summary()
        test_results["education_grading"] = await self.test_education_grading()
        test_results["email_functions"] = await self.test_email_functions()
        test_results["conversation_api"] = await self.test_conversation_api()

        # 性能测试
        test_results["performance"] = await self.performance_test()

        # 保存测试结果
        with open("test_results.json", "w", encoding="utf-8") as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)

        print("\n" + "=" * 60)
        print("✅ 完整功能测试完成！")
        print("📊 测试结果已保存到 test_results.json")
        print("\n📋 测试总结:")

        # 统计测试结果
        total_tests = 0
        passed_tests = 0

        for test_name, result in test_results.items():
            if test_name == "performance":
                continue

            total_tests += 1
            if not result.get("error"):
                passed_tests += 1
                print(f"  ✅ {test_name}: 通过")
            else:
                print(f"  ❌ {test_name}: 失败 - {result.get('error', 'Unknown error')}")

        print(f"\n🎯 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")

        await self.client.aclose()

async def main():
    """主函数"""
    tester = QwenTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
