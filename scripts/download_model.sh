#!/bin/bash

# Qwen 模型下载脚本
# 针对 Mac M2 Pro 优化的模型选择

set -e

echo "🤖 开始下载 Qwen 模型..."

# 创建模型目录
mkdir -p models

# 检查可用内存
MEMORY_GB=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024/1024)}')
echo "💾 检测到系统内存: ${MEMORY_GB}GB"

# 根据内存选择合适的模型
if [ "$MEMORY_GB" -ge 32 ]; then
    MODEL_SIZE="7B"
    MODEL_FILE="qwen1_5-7b-chat-q4_k_m.gguf"
    MODEL_URL="https://huggingface.co/Qwen/Qwen1.5-7B-Chat-GGUF/resolve/main/qwen1_5-7b-chat-q4_k_m.gguf"
    echo "🎯 选择 Qwen-1.5-7B-Chat (适合32GB+内存)"
elif [ "$MEMORY_GB" -ge 16 ]; then
    MODEL_SIZE="4B"
    MODEL_FILE="qwen1_5-4b-chat-q4_k_m.gguf"
    MODEL_URL="https://huggingface.co/Qwen/Qwen1.5-4B-Chat-GGUF/resolve/main/qwen1_5-4b-chat-q4_k_m.gguf"
    echo "🎯 选择 Qwen-1.5-4B-Chat (适合16GB+内存)"
else
    MODEL_SIZE="1.8B"
    MODEL_FILE="qwen1_5-1_8b-chat-q4_k_m.gguf"
    MODEL_URL="https://huggingface.co/Qwen/Qwen1.5-1.8B-Chat-GGUF/resolve/main/qwen1_5-1_8b-chat-q4_k_m.gguf"
    echo "🎯 选择 Qwen-1.5-1.8B-Chat (适合8GB+内存)"
fi

# 检查模型是否已存在
if [ -f "models/$MODEL_FILE" ]; then
    echo "✅ 模型已存在: models/$MODEL_FILE"
    echo "📊 模型大小: $(du -h models/$MODEL_FILE | cut -f1)"
else
    echo "📥 下载模型: $MODEL_FILE"
    echo "🔗 下载地址: $MODEL_URL"
    
    # 使用 wget 下载，支持断点续传
    if command -v wget &> /dev/null; then
        wget -c -O "models/$MODEL_FILE" "$MODEL_URL"
    elif command -v curl &> /dev/null; then
        curl -L -C - -o "models/$MODEL_FILE" "$MODEL_URL"
    else
        echo "❌ 需要安装 wget 或 curl"
        exit 1
    fi
    
    echo "✅ 模型下载完成!"
    echo "📊 模型大小: $(du -h models/$MODEL_FILE | cut -f1)"
fi

# 创建模型配置文件
cat > models/model_config.json << EOF
{
    "model_name": "Qwen-1.5-${MODEL_SIZE}-Chat",
    "model_file": "$MODEL_FILE",
    "model_path": "models/$MODEL_FILE",
    "context_length": 32768,
    "max_tokens": 2048,
    "temperature": 0.7,
    "top_p": 0.8,
    "repeat_penalty": 1.1,
    "threads": $(sysctl -n hw.ncpu),
    "gpu_layers": 0
}
EOF

echo "📝 模型配置已保存到: models/model_config.json"

# 测试模型加载
echo "🧪 测试模型加载..."
if [ -f "llama.cpp/main" ]; then
    echo "测试提示: 你好" | ./llama.cpp/main -m "models/$MODEL_FILE" -n 50 -p "你好，请简单介绍一下你自己。" --temp 0.7
    echo ""
    echo "✅ 模型测试完成!"
else
    echo "⚠️  llama.cpp 未编译，请先运行 ./scripts/setup_mac.sh"
fi

echo ""
echo "🎉 模型下载和配置完成!"
echo "📁 模型位置: models/$MODEL_FILE"
echo "⚙️  配置文件: models/model_config.json"
echo ""
echo "🚀 下一步: python src/main.py 启动服务"
