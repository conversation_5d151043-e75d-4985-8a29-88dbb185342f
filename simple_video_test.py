#!/usr/bin/env python3
"""
简单的视频处理测试 - 避免崩溃
"""

import requests
import os
import sys

def check_service():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务运行正常")
            return True
        else:
            print(f"❌ 服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        print("请确保服务正在运行: python fast_start.py")
        return False

def check_video_info():
    """检查视频处理信息"""
    print("\n📋 检查视频处理能力...")
    
    try:
        response = requests.get("http://localhost:8000/video_processing_info", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ 视频处理信息:")
            print(f"   支持格式: {', '.join(data.get('supported_formats', []))}")
            print(f"   最大文件: {data.get('max_file_size_mb', 0)}MB")
            
            deps = data.get('dependencies', {})
            print("   依赖状态:")
            for dep, available in deps.items():
                status = "✅" if available else "❌"
                print(f"     {dep}: {status}")
            
            return deps.get('ffmpeg', False)
            
        else:
            print(f"❌ 获取信息失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_subtitle_translation():
    """测试字幕翻译功能"""
    print("\n📝 测试字幕翻译功能...")
    
    # 简单的SRT字幕
    test_srt = """1
00:00:01,000 --> 00:00:04,000
Hello, welcome to our tutorial.

2
00:00:05,000 --> 00:00:08,000
Today we will learn about programming."""
    
    try:
        response = requests.post(
            "http://localhost:8000/translate_subtitle",
            json={
                "subtitle_content": test_srt,
                "target_language": "zh",
                "bilingual": False
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 字幕翻译成功")
            print(f"   翻译了 {data.get('translated_count', 0)} 条字幕")
            
            translated = data.get('translated_srt', '')
            if translated:
                print("\n📝 翻译结果:")
                print("-" * 40)
                print(translated)
                print("-" * 40)
            
            return True
        else:
            print(f"❌ 字幕翻译失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 字幕翻译异常: {e}")
        return False

def find_test_video():
    """查找测试视频文件"""
    print("\n🔍 查找测试视频文件...")
    
    # 可能的视频文件位置
    possible_files = [
        "test.mp4",
        "sample.mp4", 
        "demo.mp4",
        os.path.expanduser("~/Desktop/test.mp4"),
        os.path.expanduser("~/Downloads/sample.mp4"),
    ]
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"✅ 找到视频文件: {file_path} ({size_mb:.1f}MB)")
            
            if size_mb > 100:
                print(f"⚠️  文件太大 ({size_mb:.1f}MB > 100MB)")
                continue
            
            return file_path
    
    print("❌ 未找到合适的测试视频文件")
    print("\n💡 建议:")
    print("1. 将一个小的MP4文件重命名为 test.mp4 放在当前目录")
    print("2. 确保文件小于100MB")
    print("3. 支持的格式: MP4, AVI, MOV, MKV等")
    
    return None

def test_video_upload(video_file):
    """测试视频上传"""
    print(f"\n📤 测试视频上传: {video_file}")
    
    if not os.path.exists(video_file):
        print(f"❌ 文件不存在: {video_file}")
        return False
    
    file_size = os.path.getsize(video_file) / (1024 * 1024)
    print(f"   文件大小: {file_size:.2f}MB")
    
    try:
        with open(video_file, 'rb') as f:
            files = {'file': (os.path.basename(video_file), f, 'video/mp4')}
            data = {
                'target_language': 'zh',
                'extract_method': 'whisper'
            }
            
            print("🔄 上传并处理视频...")
            
            response = requests.post(
                "http://localhost:8000/process_video",
                files=files,
                data=data,
                timeout=120  # 2分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print("✅ 视频处理成功")
                print(f"   视频文件: {result.get('video_file', '')}")
                print(f"   处理步骤: {', '.join(result.get('processing_steps', []))}")
                
                # 显示字幕
                original = result.get('original_subtitles', '')
                translated = result.get('translated_subtitles', '')
                
                if original:
                    print("\n📝 原始字幕:")
                    print("-" * 40)
                    print(original[:300] + "..." if len(original) > 300 else original)
                    print("-" * 40)
                
                if translated:
                    print("\n🌐 翻译字幕:")
                    print("-" * 40)
                    print(translated[:300] + "..." if len(translated) > 300 else translated)
                    print("-" * 40)
                
                return True
                
            else:
                print(f"❌ 视频处理失败: {response.status_code}")
                error_text = response.text
                print(f"错误信息: {error_text}")
                
                # 如果是字幕相关错误，给出建议
                if "字幕" in error_text or "subtitle" in error_text.lower():
                    print("\n💡 建议:")
                    print("1. 尝试使用包含字幕的视频文件")
                    print("2. 或者先使用字幕翻译功能")
                
                return False
                
    except Exception as e:
        print(f"❌ 视频上传异常: {e}")
        return False

def main():
    """主测试流程"""
    print("🎥 简单视频处理测试")
    print("=" * 60)
    
    # 1. 检查服务
    if not check_service():
        return
    
    # 2. 检查视频处理能力
    ffmpeg_ok = check_video_info()
    
    # 3. 测试字幕翻译
    subtitle_ok = test_subtitle_translation()
    
    # 4. 如果有FFmpeg，测试视频处理
    if ffmpeg_ok:
        video_file = find_test_video()
        if video_file:
            video_ok = test_video_upload(video_file)
        else:
            video_ok = False
            print("\n⚠️  跳过视频上传测试（没有合适的测试文件）")
    else:
        video_ok = False
        print("\n⚠️  跳过视频处理测试（FFmpeg不可用）")
    
    # 5. 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   服务状态: ✅")
    print(f"   字幕翻译: {'✅' if subtitle_ok else '❌'}")
    print(f"   视频处理: {'✅' if video_ok else '❌' if ffmpeg_ok else '⚠️ '}")
    
    if subtitle_ok:
        print("\n🎉 基础功能正常！")
        print("\n💡 使用方法:")
        print("1. 字幕翻译: POST /translate_subtitle")
        print("2. 视频处理: POST /process_video (需要FFmpeg)")
        print("3. 支持格式: SRT字幕, MP4/AVI/MOV视频")
    else:
        print("\n❌ 基础功能异常，请检查服务")

if __name__ == "__main__":
    main()
