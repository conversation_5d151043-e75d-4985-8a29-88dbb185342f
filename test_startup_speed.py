#!/usr/bin/env python3
"""
测试启动速度对比
"""

import time
import subprocess
import requests
import signal
import os
from typing import Optional

class StartupSpeedTester:
    """启动速度测试器"""
    
    def __init__(self):
        self.processes = []
    
    def test_startup_time(self, command: str, name: str, timeout: int = 120) -> Optional[float]:
        """测试启动时间"""
        print(f"\n🚀 测试 {name} 启动时间...")
        print(f"命令: {command}")
        
        start_time = time.time()
        
        try:
            # 启动进程
            process = subprocess.Popen(
                command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes.append(process)
            
            # 等待服务可用
            service_ready = False
            check_interval = 2  # 每2秒检查一次
            max_checks = timeout // check_interval
            
            for i in range(max_checks):
                time.sleep(check_interval)
                
                try:
                    response = requests.get("http://localhost:8000/health", timeout=5)
                    if response.status_code == 200:
                        service_ready = True
                        break
                except:
                    continue
                
                # 检查进程是否还在运行
                if process.poll() is not None:
                    print(f"❌ 进程意外退出")
                    return None
            
            startup_time = time.time() - start_time
            
            if service_ready:
                print(f"✅ {name} 启动成功")
                print(f"⏱️  启动时间: {startup_time:.1f}s")
                
                # 测试基本功能
                try:
                    test_response = requests.post(
                        "http://localhost:8000/chat",
                        json={"message": "hi", "max_tokens": 5},
                        timeout=10
                    )
                    if test_response.status_code == 200:
                        print("✅ 基本功能正常")
                    else:
                        print("⚠️  基本功能异常")
                except Exception as e:
                    print(f"⚠️  功能测试失败: {e}")
                
                return startup_time
            else:
                print(f"❌ {name} 启动超时 ({timeout}s)")
                return None
                
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return None
        finally:
            # 停止进程
            self.stop_process(process)
    
    def stop_process(self, process):
        """停止进程"""
        try:
            if process.poll() is None:
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                process.wait()
        except:
            pass
    
    def cleanup(self):
        """清理所有进程"""
        for process in self.processes:
            self.stop_process(process)
        self.processes.clear()
    
    def run_comparison_test(self):
        """运行对比测试"""
        print("⚡ Qwen LLM Platform 启动速度对比测试")
        print("=" * 60)
        
        # 检查环境
        if not os.path.exists("venv"):
            print("❌ 虚拟环境不存在")
            return
        
        if not os.path.exists("models/model_config.json"):
            print("❌ 模型配置不存在")
            return
        
        results = {}
        
        # 测试配置
        test_configs = [
            {
                "command": "python fast_start.py",
                "name": "快速启动模式",
                "timeout": 60
            },
            {
                "command": "./start_optimized.sh",
                "name": "优化启动模式", 
                "timeout": 120
            },
            {
                "command": "./start_from_root.py",
                "name": "标准启动模式",
                "timeout": 180
            }
        ]
        
        for config in test_configs:
            # 检查命令是否存在
            command_file = config["command"].split()[0]
            if command_file.startswith("./") and not os.path.exists(command_file):
                print(f"⚠️  跳过 {config['name']} - 文件不存在: {command_file}")
                continue
            
            startup_time = self.test_startup_time(
                config["command"],
                config["name"],
                config["timeout"]
            )
            
            if startup_time:
                results[config["name"]] = startup_time
            
            # 等待一下再测试下一个
            time.sleep(3)
        
        # 显示结果
        self.show_results(results)
    
    def show_results(self, results: dict):
        """显示测试结果"""
        if not results:
            print("\n❌ 没有成功的测试结果")
            return
        
        print("\n" + "=" * 60)
        print("📊 启动速度对比结果")
        print("=" * 60)
        
        # 按启动时间排序
        sorted_results = sorted(results.items(), key=lambda x: x[1])
        
        fastest_time = sorted_results[0][1]
        
        for i, (name, time_taken) in enumerate(sorted_results):
            speedup = fastest_time / time_taken if time_taken > 0 else 0
            rank = "🥇" if i == 0 else "🥈" if i == 1 else "🥉" if i == 2 else f"{i+1}."
            
            print(f"{rank} {name}")
            print(f"   启动时间: {time_taken:.1f}s")
            if i > 0:
                print(f"   相对最快: {speedup:.1f}x 慢")
            else:
                print(f"   🚀 最快启动")
            print()
        
        # 分析和建议
        print("💡 分析和建议:")
        print("-" * 40)
        
        if "快速启动模式" in results:
            fast_time = results["快速启动模式"]
            if fast_time < 30:
                print("✅ 快速模式启动优秀 (<30s)")
            elif fast_time < 60:
                print("👍 快速模式启动良好 (<60s)")
            else:
                print("⚠️  快速模式仍需优化")
        
        if len(results) >= 2:
            times = list(results.values())
            improvement = (max(times) - min(times)) / max(times) * 100
            print(f"📈 最大改进幅度: {improvement:.1f}%")
        
        print("\n🎯 使用建议:")
        print("- 开发测试: 使用快速启动模式")
        print("- 生产环境: 使用优化启动模式")
        print("- 功能完整: 使用标准启动模式")

def main():
    """主函数"""
    tester = StartupSpeedTester()
    
    try:
        tester.run_comparison_test()
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
    finally:
        print("\n🧹 清理测试环境...")
        tester.cleanup()
        print("✅ 清理完成")

if __name__ == "__main__":
    main()
