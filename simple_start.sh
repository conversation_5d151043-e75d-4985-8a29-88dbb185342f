#!/bin/bash

# 简化启动脚本 - 跳过依赖安装

set -e

echo "🚀 Qwen LLM Platform 简化启动"
echo "============================="

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在"
    echo "🔄 尝试使用系统 Python..."
    PYTHON_CMD="python3"
else
    echo "🐍 激活虚拟环境..."
    source venv/bin/activate
    PYTHON_CMD="python"
fi

# 检查模型文件
if [ ! -f "models/model_config.json" ]; then
    echo "❌ 模型配置不存在"
    echo "📊 当前模型目录内容:"
    ls -la models/ 2>/dev/null || echo "模型目录不存在"
    exit 1
fi

echo "📊 模型信息:"
echo "配置文件: models/model_config.json"
if command -v jq &> /dev/null; then
    cat models/model_config.json | jq .
else
    cat models/model_config.json
fi

echo ""
echo "模型文件:"
ls -lh models/*.gguf 2>/dev/null || echo "未找到 .gguf 文件"

echo ""
echo "🎯 启动选项："
echo "1) 启动 API 服务"
echo "2) 测试 Python 导入"
echo "3) 查看系统信息"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🚀 启动 API 服务..."
        echo "📖 API 文档: http://localhost:8000/docs"
        echo "🔍 健康检查: http://localhost:8000/health"
        echo ""
        echo "按 Ctrl+C 停止服务"
        echo ""
        
        cd src
        $PYTHON_CMD main.py
        ;;
    
    2)
        echo "🧪 测试 Python 导入..."
        $PYTHON_CMD -c "
import sys
print(f'Python 版本: {sys.version}')
print(f'Python 路径: {sys.executable}')

try:
    import fastapi
    print('✅ FastAPI 可用')
except ImportError as e:
    print(f'❌ FastAPI 不可用: {e}')

try:
    import httpx
    print('✅ HTTPX 可用')
except ImportError as e:
    print(f'❌ HTTPX 不可用: {e}')

try:
    import uvicorn
    print('✅ Uvicorn 可用')
except ImportError as e:
    print(f'❌ Uvicorn 不可用: {e}')

try:
    import llama_cpp
    print('✅ llama-cpp-python 可用')
except ImportError as e:
    print(f'❌ llama-cpp-python 不可用: {e}')
"
        ;;
    
    3)
        echo "📊 系统信息:"
        echo "操作系统: $(uname -s) $(uname -r)"
        echo "架构: $(uname -m)"
        echo "Python 版本: $($PYTHON_CMD --version)"
        echo "Python 路径: $(which $PYTHON_CMD)"
        echo ""
        echo "虚拟环境状态:"
        if [ -n "$VIRTUAL_ENV" ]; then
            echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"
        else
            echo "❌ 未使用虚拟环境"
        fi
        echo ""
        echo "当前目录: $(pwd)"
        echo "项目文件:"
        ls -la src/ models/ 2>/dev/null || echo "项目文件不完整"
        ;;
    
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
