#!/usr/bin/env python3
"""
诊断服务问题
"""

import requests
import json

def check_service_health():
    """检查服务健康状态"""
    print("🔍 检查服务状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 服务运行正常")
            print(f"   模型加载状态: {data.get('model_loaded', 'unknown')}")
            print(f"   服务状态: {data.get('status', 'unknown')}")
            
            if 'model_stats' in data.get('system_info', {}):
                stats = data['system_info']['model_stats']
                print(f"   总请求数: {stats.get('total_requests', 0)}")
                print(f"   缓存命中率: {stats.get('cache_hit_rate', '0%')}")
            
            return True
        else:
            print(f"❌ 服务状态异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接服务: {e}")
        return False

def test_simple_request():
    """测试简单请求"""
    print("\n🧪 测试简单请求...")
    
    simple_request = {
        "message": "hello",
        "max_tokens": 10,
        "temperature": 0.3
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json=simple_request,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 简单请求成功")
            print(f"   响应: {data.get('response', '')}")
            print(f"   Token: {data.get('tokens_used', 0)}")
            print(f"   时间: {data.get('processing_time', 0)}s")
            return True
        else:
            print(f"❌ 简单请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 简单请求异常: {e}")
        return False

def check_config():
    """检查配置文件"""
    print("\n📋 检查配置文件...")
    
    config_path = "models/model_config.json"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件读取成功")
        print(f"   context_length: {config.get('context_length', 'unknown')}")
        print(f"   max_tokens: {config.get('max_tokens', 'unknown')}")
        print(f"   model_path: {config.get('model_path', 'unknown')}")
        
        # 检查模型文件
        model_path = config.get('model_path', '')
        if model_path and os.path.exists(model_path):
            import os
            size_mb = os.path.getsize(model_path) / (1024 * 1024)
            print(f"   模型文件大小: {size_mb:.1f}MB")
        else:
            print(f"   ❌ 模型文件不存在: {model_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件错误: {e}")
        return False

def restore_working_config():
    """恢复可工作的配置"""
    print("\n🔧 恢复可工作的配置...")
    
    config_path = "models/model_config.json"
    
    try:
        # 读取当前配置获取模型路径
        with open(config_path, 'r', encoding='utf-8') as f:
            current_config = json.load(f)
        
        model_path = current_config.get('model_path', '')
        
        # 创建保守的工作配置
        working_config = {
            "model_name": "Qwen-1.5-4B-Chat",
            "model_file": "qwen1_5-4b-chat-q4_k_m.gguf",
            "model_path": model_path,
            
            # 保守的参数设置
            "context_length": 1024,        # 适中的上下文
            "max_tokens": 512,             # 适中的输出
            "temperature": 0.7,            # 适中的创造性
            "top_p": 0.9,
            "repeat_penalty": 1.05,
            
            # 稳定的系统参数
            "threads": 4,
            "n_batch": 256,
            "use_mmap": True,
            "use_mlock": False,
            
            # 基础设置
            "gpu_layers": 0,
            "enable_parallel": True,
            "max_workers": 1,
            "lazy_loading": True
        }
        
        # 备份当前配置
        backup_path = config_path + ".broken"
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(current_config, f, indent=2, ensure_ascii=False)
        
        # 保存工作配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(working_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 工作配置已恢复")
        print(f"   备份损坏配置: {backup_path}")
        print(f"   新配置参数: context_length={working_config['context_length']}, max_tokens={working_config['max_tokens']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 恢复配置失败: {e}")
        return False

def main():
    """主诊断流程"""
    print("🔍 Qwen LLM Platform 服务诊断")
    print("=" * 50)
    
    # 1. 检查服务健康状态
    service_ok = check_service_health()
    
    # 2. 检查配置文件
    config_ok = check_config()
    
    # 3. 测试简单请求
    if service_ok:
        simple_ok = test_simple_request()
    else:
        simple_ok = False
    
    # 4. 分析问题
    print("\n📊 诊断结果:")
    print(f"   服务状态: {'✅' if service_ok else '❌'}")
    print(f"   配置文件: {'✅' if config_ok else '❌'}")
    print(f"   简单请求: {'✅' if simple_ok else '❌'}")
    
    # 5. 提供解决方案
    if not simple_ok:
        print("\n🔧 建议解决方案:")
        
        if not service_ok:
            print("1. 服务未正常运行，请重启服务")
        elif not config_ok:
            print("1. 配置文件有问题，建议恢复默认配置")
        else:
            print("1. 模型处理有问题，建议恢复保守配置")
        
        choice = input("\n是否恢复可工作的配置？(y/N): ")
        if choice.lower() == 'y':
            if restore_working_config():
                print("\n✅ 配置已恢复，请重启服务:")
                print("   python fast_start.py")
    else:
        print("\n✅ 基础功能正常，长文本问题可能是参数设置过激进")
        print("建议:")
        print("1. 使用较小的 max_tokens (如 500-1000)")
        print("2. 降低 temperature (如 0.7)")
        print("3. 分段请求长文本")

if __name__ == "__main__":
    import os
    main()
