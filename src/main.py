#!/usr/bin/env python3
"""
Qwen LLM Platform - 主服务入口
基于 FastAPI 的统一 AI 服务接口
"""

import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

try:
    from models.optimized_qwen_model import OptimizedQwenModel as QwenModel
    print("✅ 使用优化版模型（支持缓存和并行）")
except ImportError:
    from models.qwen_model import QwenModel
    print("⚠️  使用基础版模型")
from api.chat import chat_router
from api.tasks import tasks_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局模型实例
qwen_model: Optional[QwenModel] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global qwen_model
    
    # 启动时初始化模型
    logger.info("🚀 启动 Qwen LLM Platform...")
    try:
        qwen_model = QwenModel()
        await qwen_model.load_model()
        logger.info("✅ 模型加载成功")
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        raise
    
    yield
    
    # 关闭时清理资源
    logger.info("🔄 关闭 Qwen LLM Platform...")
    if qwen_model:
        qwen_model.cleanup()

# 创建 FastAPI 应用
app = FastAPI(
    title="Qwen LLM Platform",
    description="基于 Qwen 的开源大模型服务平台",
    version="1.0.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1024
    system_prompt: Optional[str] = None

class SummarizeRequest(BaseModel):
    text: str
    max_length: Optional[int] = 200
    language: Optional[str] = "zh"

class TranslateRequest(BaseModel):
    text: str
    target_language: str = "zh"
    source_language: Optional[str] = "auto"

class GradeRequest(BaseModel):
    assignment_text: str
    student_answer: str
    subject: str = "数学"
    grade_level: str = "高中"
    max_tokens: Optional[int] = 1000

class SubtitleTranslateRequest(BaseModel):
    subtitle_content: str
    subtitle_format: str = "srt"  # srt 或 vtt
    target_language: str = "zh"
    source_language: str = "auto"
    bilingual: bool = False  # 是否生成双语字幕

# 响应模型
class ChatResponse(BaseModel):
    response: str
    model: str
    tokens_used: int
    processing_time: float

class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    version: str
    system_info: Dict[str, Any]

# 路由
@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    return {
        "message": "Qwen LLM Platform API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    import psutil
    import platform
    
    health_response = HealthResponse(
        status="healthy" if qwen_model and qwen_model.is_loaded else "unhealthy",
        model_loaded=qwen_model.is_loaded if qwen_model else False,
        version="1.0.0",
        system_info={
            "platform": platform.system(),
            "architecture": platform.machine(),
            "cpu_count": psutil.cpu_count(),
            "memory_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "python_version": platform.python_version()
        }
    )

    # 添加优化模型统计
    if qwen_model and hasattr(qwen_model, 'get_stats'):
        health_response.system_info["model_stats"] = qwen_model.get_stats()

    return health_response

@app.get("/stats")
async def get_performance_stats():
    """获取性能统计"""
    if not qwen_model:
        raise HTTPException(status_code=503, detail="模型未加载")

    stats = {"timestamp": datetime.now().isoformat()}

    # 模型统计
    if hasattr(qwen_model, 'get_stats'):
        stats["model"] = qwen_model.get_stats()

    # 系统统计
    try:
        import psutil
        stats["system"] = {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total_gb": round(psutil.virtual_memory().total / (1024**3), 1),
                "used_gb": round(psutil.virtual_memory().used / (1024**3), 1),
                "percent": psutil.virtual_memory().percent
            }
        }
    except ImportError:
        stats["system"] = {"message": "psutil 不可用"}

    return stats

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """基础对话接口"""
    if not qwen_model or not qwen_model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")
    
    try:
        result = await qwen_model.generate(
            prompt=request.message,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            system_prompt=request.system_prompt
        )
        
        return ChatResponse(
            response=result["text"],
            model=result["model"],
            tokens_used=result["tokens_used"],
            processing_time=result["processing_time"]
        )
    except Exception as e:
        logger.error(f"对话生成错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/summarize")
async def summarize(request: SummarizeRequest):
    """文档摘要"""
    if not qwen_model or not qwen_model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")
    
    system_prompt = f"请用{request.language}对以下内容进行摘要，控制在{request.max_length}字以内："
    
    try:
        result = await qwen_model.generate(
            prompt=request.text,
            system_prompt=system_prompt,
            max_tokens=(request.max_length or 200) * 2
        )
        
        return {"summary": result["text"], "original_length": len(request.text)}
    except Exception as e:
        logger.error(f"摘要生成错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/translate")
async def translate(request: TranslateRequest):
    """文本翻译"""
    if not qwen_model or not qwen_model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")
    
    system_prompt = f"请将以下文本翻译成{request.target_language}："
    
    try:
        result = await qwen_model.generate(
            prompt=request.text,
            system_prompt=system_prompt,
            max_tokens=len(request.text) * 2
        )
        
        return {
            "translated_text": result["text"],
            "source_language": request.source_language,
            "target_language": request.target_language
        }
    except Exception as e:
        logger.error(f"翻译错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/grade")
async def grade_assignment(request: GradeRequest):
    """作业批改接口"""
    if not qwen_model or not qwen_model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")

    # 构建专业的批改系统提示
    system_prompt = f"""你是一位专业的{request.subject}老师，正在批改{request.grade_level}学生的作业。

请按以下格式进行批改：

1. 【总体评价】：给出总分和整体表现
2. 【逐题分析】：
   - 对每道题进行详细分析
   - 指出正确答案
   - 评价学生答案的对错
   - 给出具体分数
3. 【优点】：指出学生答案的亮点
4. 【改进建议】：提出具体的改进意见
5. 【学习建议】：给出进一步学习的建议

请以鼓励性和建设性的语调进行评价，帮助学生提高学习效果。"""

    # 构建批改内容
    grading_content = f"""
题目内容：
{request.assignment_text}

学生答案：
{request.student_answer}

请对以上作业进行详细批改。"""

    try:
        result = await qwen_model.generate(
            prompt=grading_content,
            system_prompt=system_prompt,
            max_tokens=request.max_tokens,
            temperature=0.3  # 使用较低的温度确保批改的一致性
        )

        return {
            "grading_result": result["text"],
            "subject": request.subject,
            "grade_level": request.grade_level,
            "model": result["model"],
            "tokens_used": result["tokens_used"],
            "processing_time": result["processing_time"],
            "graded_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"批改错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/translate_subtitle")
async def translate_subtitle(request: SubtitleTranslateRequest):
    """字幕翻译接口"""
    if not qwen_model or not qwen_model.is_loaded:
        raise HTTPException(status_code=503, detail="模型未加载")

    try:
        # 导入字幕翻译器
        from integrations.subtitle_translation import SubtitleTranslator

        translator = SubtitleTranslator()

        if request.bilingual:
            # 生成双语字幕
            result = await translator.create_bilingual_subtitles(
                request.subtitle_content,
                request.target_language
            )
        elif request.subtitle_format.lower() == "vtt":
            # 翻译VTT格式
            result = await translator.translate_vtt_file(
                request.subtitle_content,
                request.target_language
            )
        else:
            # 翻译SRT格式
            result = await translator.translate_srt_file(
                request.subtitle_content,
                request.target_language,
                request.source_language
            )

        await translator.close()

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return result

    except Exception as e:
        logger.error(f"字幕翻译错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 包含其他路由
app.include_router(chat_router, prefix="/api/v1")
app.include_router(tasks_router, prefix="/api/v1")

def main():
    """主函数"""
    # 从环境变量读取配置
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    
    logger.info(f"🌐 启动服务: http://{host}:{port}")
    logger.info(f"📖 API 文档: http://{host}:{port}/docs")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
