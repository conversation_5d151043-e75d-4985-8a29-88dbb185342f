"""
模型并行处理池 - 支持多个模型实例并行处理请求
"""

import asyncio
import threading
import time
import queue
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor, Future
import multiprocessing
from dataclasses import dataclass
import uuid

@dataclass
class ModelRequest:
    """模型请求"""
    request_id: str
    prompt: str
    max_tokens: int
    temperature: float
    system_prompt: Optional[str] = None
    priority: int = 1  # 1=低, 2=中, 3=高

@dataclass
class ModelResponse:
    """模型响应"""
    request_id: str
    response: Dict[str, Any]
    processing_time: float
    worker_id: int

class ModelWorker:
    """模型工作进程"""
    
    def __init__(self, worker_id: int, model_path: str, config: Dict[str, Any]):
        self.worker_id = worker_id
        self.model_path = model_path
        self.config = config
        self.model = None
        self.is_ready = False
        self.request_count = 0
        
    def initialize(self):
        """初始化模型"""
        try:
            from llama_cpp import Llama
            
            print(f"🔄 Worker {self.worker_id}: 初始化模型...")
            
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=self.config.get("context_length", 512),
                n_threads=max(1, multiprocessing.cpu_count() // 4),  # 每个worker使用部分CPU
                n_batch=self.config.get("n_batch", 128),
                use_mmap=True,
                use_mlock=False,  # 避免内存锁定冲突
                verbose=False
            )
            
            # 预热
            self.model("test", max_tokens=1)
            
            self.is_ready = True
            print(f"✅ Worker {self.worker_id}: 模型初始化完成")
            
        except Exception as e:
            print(f"❌ Worker {self.worker_id}: 初始化失败 - {e}")
            self.is_ready = False
    
    def process_request(self, request: ModelRequest) -> ModelResponse:
        """处理请求"""
        if not self.is_ready:
            return ModelResponse(
                request_id=request.request_id,
                response={"error": "模型未就绪"},
                processing_time=0,
                worker_id=self.worker_id
            )
        
        start_time = time.time()
        
        try:
            result = self.model(
                request.prompt,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                stop=["\\n\\n", "。", ".", "!", "?"],
                echo=False
            )
            
            processing_time = time.time() - start_time
            self.request_count += 1
            
            response_data = {
                "text": result["choices"][0]["text"].strip(),
                "tokens_used": result["usage"]["total_tokens"],
                "processing_time": processing_time,
                "model": f"Qwen-Worker-{self.worker_id}",
                "worker_id": self.worker_id
            }
            
            return ModelResponse(
                request_id=request.request_id,
                response=response_data,
                processing_time=processing_time,
                worker_id=self.worker_id
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            return ModelResponse(
                request_id=request.request_id,
                response={"error": str(e)},
                processing_time=processing_time,
                worker_id=self.worker_id
            )

class ModelPool:
    """模型池管理器"""
    
    def __init__(self, model_path: str, config: Dict[str, Any], pool_size: int = 2):
        self.model_path = model_path
        self.config = config
        self.pool_size = min(pool_size, multiprocessing.cpu_count() // 2)  # 限制池大小
        
        # 请求队列（按优先级）
        self.high_priority_queue = queue.PriorityQueue()
        self.normal_queue = queue.Queue()
        
        # 响应映射
        self.pending_requests: Dict[str, Future] = {}
        
        # 工作线程池
        self.executor = ThreadPoolExecutor(max_workers=self.pool_size, thread_name_prefix="ModelWorker")
        
        # 工作进程
        self.workers: List[ModelWorker] = []
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0,
            "queue_size": 0
        }
        
        # 初始化工作进程
        self._initialize_workers()
        
        # 启动请求处理循环
        self._start_processing_loop()
    
    def _initialize_workers(self):
        """初始化工作进程"""
        print(f"🔄 初始化 {self.pool_size} 个模型工作进程...")
        
        for i in range(self.pool_size):
            worker = ModelWorker(i, self.model_path, self.config)
            
            # 在线程池中初始化
            future = self.executor.submit(worker.initialize)
            
            self.workers.append(worker)
        
        # 等待所有工作进程就绪
        ready_count = 0
        max_wait = 60  # 最多等待60秒
        
        for _ in range(max_wait):
            ready_count = sum(1 for w in self.workers if w.is_ready)
            if ready_count == self.pool_size:
                break
            time.sleep(1)
        
        print(f"✅ {ready_count}/{self.pool_size} 个工作进程就绪")
    
    def _start_processing_loop(self):
        """启动请求处理循环"""
        def processing_loop():
            while True:
                try:
                    # 优先处理高优先级请求
                    request = None
                    
                    try:
                        # 尝试获取高优先级请求
                        _, request = self.high_priority_queue.get_nowait()
                    except queue.Empty:
                        try:
                            # 获取普通请求
                            request = self.normal_queue.get(timeout=1)
                        except queue.Empty:
                            continue
                    
                    if request:
                        # 找到可用的工作进程
                        available_worker = self._get_available_worker()
                        if available_worker:
                            # 提交处理任务
                            future = self.executor.submit(available_worker.process_request, request)
                            self.pending_requests[request.request_id] = future
                        else:
                            # 没有可用工作进程，重新排队
                            if request.priority >= 3:
                                self.high_priority_queue.put((request.priority, request))
                            else:
                                self.normal_queue.put(request)
                            time.sleep(0.1)
                
                except Exception as e:
                    print(f"处理循环错误: {e}")
                    time.sleep(0.1)
        
        thread = threading.Thread(target=processing_loop, daemon=True)
        thread.start()
    
    def _get_available_worker(self) -> Optional[ModelWorker]:
        """获取可用的工作进程"""
        # 简单的轮询策略
        for worker in self.workers:
            if worker.is_ready:
                return worker
        return None
    
    async def submit_request(self, prompt: str, max_tokens: int = 128, 
                           temperature: float = 0.3, system_prompt: Optional[str] = None,
                           priority: int = 1) -> Dict[str, Any]:
        """提交请求"""
        request_id = str(uuid.uuid4())
        
        request = ModelRequest(
            request_id=request_id,
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            system_prompt=system_prompt,
            priority=priority
        )
        
        # 根据优先级排队
        if priority >= 3:
            self.high_priority_queue.put((priority, request))
        else:
            self.normal_queue.put(request)
        
        self.stats["total_requests"] += 1
        self.stats["queue_size"] = self.normal_queue.qsize() + self.high_priority_queue.qsize()
        
        # 等待结果
        max_wait = 30  # 最多等待30秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            if request_id in self.pending_requests:
                future = self.pending_requests[request_id]
                if future.done():
                    try:
                        response = future.result()
                        del self.pending_requests[request_id]
                        
                        # 更新统计
                        if "error" not in response.response:
                            self.stats["completed_requests"] += 1
                        else:
                            self.stats["failed_requests"] += 1
                        
                        # 更新平均处理时间
                        total_completed = self.stats["completed_requests"]
                        if total_completed > 0:
                            current_avg = self.stats["average_processing_time"]
                            new_avg = (current_avg * (total_completed - 1) + response.processing_time) / total_completed
                            self.stats["average_processing_time"] = new_avg
                        
                        return response.response
                        
                    except Exception as e:
                        del self.pending_requests[request_id]
                        self.stats["failed_requests"] += 1
                        return {"error": f"处理失败: {e}"}
            
            await asyncio.sleep(0.1)
        
        # 超时
        if request_id in self.pending_requests:
            del self.pending_requests[request_id]
        
        self.stats["failed_requests"] += 1
        return {"error": "请求超时"}
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        ready_workers = sum(1 for w in self.workers if w.is_ready)
        
        return {
            "pool_size": self.pool_size,
            "ready_workers": ready_workers,
            "queue_size": self.stats["queue_size"],
            "total_requests": self.stats["total_requests"],
            "completed_requests": self.stats["completed_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{(self.stats['completed_requests'] / max(1, self.stats['total_requests']) * 100):.1f}%",
            "average_processing_time": f"{self.stats['average_processing_time']:.2f}s",
            "pending_requests": len(self.pending_requests)
        }
    
    def shutdown(self):
        """关闭模型池"""
        print("🛑 关闭模型池...")
        self.executor.shutdown(wait=True)
        print("✅ 模型池已关闭")

# 全局模型池实例
model_pool: Optional[ModelPool] = None

def initialize_model_pool(model_path: str, config: Dict[str, Any], pool_size: int = 2):
    """初始化全局模型池"""
    global model_pool
    
    if model_pool is None:
        print(f"🔄 初始化模型池 (大小: {pool_size})...")
        model_pool = ModelPool(model_path, config, pool_size)
        print("✅ 模型池初始化完成")
    
    return model_pool

def get_model_pool() -> Optional[ModelPool]:
    """获取全局模型池"""
    return model_pool
