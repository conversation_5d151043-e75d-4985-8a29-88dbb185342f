"""
轻量级模型池 - 快速初始化，按需加载
"""

import asyncio
import threading
import time
import queue
import os
import multiprocessing
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
import uuid

@dataclass
class LightRequest:
    """轻量级请求"""
    request_id: str
    prompt: str
    max_tokens: int
    temperature: float
    priority: int = 1

class LazyModelWorker:
    """懒加载模型工作器"""
    
    def __init__(self, worker_id: int, model_path: str, config: Dict[str, Any]):
        self.worker_id = worker_id
        self.model_path = model_path
        self.config = config
        self.model = None
        self.is_ready = False
        self.is_loading = False
        self.request_count = 0
        self.last_used = time.time()
        
    def ensure_loaded(self):
        """确保模型已加载（懒加载）"""
        if self.is_ready:
            return True
            
        if self.is_loading:
            # 等待加载完成
            max_wait = 30
            start_time = time.time()
            while self.is_loading and (time.time() - start_time) < max_wait:
                time.sleep(0.1)
            return self.is_ready
        
        # 开始加载
        self.is_loading = True
        try:
            print(f"⚡ Worker {self.worker_id}: 快速加载模型...")
            start_time = time.time()
            
            from llama_cpp import Llama
            
            # 长文本优化配置
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=self.config.get("context_length", 4096),  # 支持长上下文
                n_threads=min(4, os.cpu_count() or 4),  # 增加线程数
                n_batch=512,  # 增加批处理大小
                use_mmap=True,
                use_mlock=False,
                verbose=False
            )
            
            load_time = time.time() - start_time
            self.is_ready = True
            print(f"✅ Worker {self.worker_id}: 加载完成 ({load_time:.1f}s)")
            
        except Exception as e:
            print(f"❌ Worker {self.worker_id}: 加载失败 - {e}")
            self.is_ready = False
        finally:
            self.is_loading = False
        
        return self.is_ready
    
    def process_request(self, request: LightRequest) -> Dict[str, Any]:
        """处理请求"""
        if not self.ensure_loaded():
            return {"error": "模型加载失败"}
        
        start_time = time.time()
        self.last_used = start_time
        
        try:
            result = self.model(
                request.prompt,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                stop=["<|endoftext|>", "<|im_end|>"],  # 只保留必要的停止词，允许长文本
                echo=False
            )

            processing_time = time.time() - start_time
            self.request_count += 1

            # 清理响应文本
            response_text = result["choices"][0]["text"].strip()
            # 移除多余的换行符和转义字符
            response_text = response_text.replace("\\n", "\n").replace("\\\\", "\\")
            # 移除开头的换行符
            response_text = response_text.lstrip("\n")

            return {
                "text": response_text,
                "tokens_used": result.get("usage", {}).get("total_tokens", 0),
                "processing_time": processing_time,
                "model": f"Qwen-Light-{self.worker_id}",
                "worker_id": self.worker_id
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {"error": str(e), "processing_time": processing_time}
    
    def unload(self):
        """卸载模型释放内存"""
        if self.model:
            del self.model
            self.model = None
            self.is_ready = False
            print(f"🗑️  Worker {self.worker_id}: 模型已卸载")

class LightweightModelPool:
    """轻量级模型池"""
    
    def __init__(self, model_path: str, config: Dict[str, Any], max_workers: int = 2):
        self.model_path = model_path
        self.config = config
        self.max_workers = max_workers
        
        # 请求队列
        self.request_queue = queue.Queue()
        self.response_futures: Dict[str, asyncio.Future] = {}
        
        # 工作器池（懒初始化）
        self.workers: List[LazyModelWorker] = []
        self.worker_lock = threading.Lock()
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="LightWorker")
        
        # 统计
        self.stats = {
            "total_requests": 0,
            "completed_requests": 0,
            "failed_requests": 0,
            "active_workers": 0,
            "average_processing_time": 0.0
        }
        
        # 立即创建工作器（但不加载模型）
        self._create_workers()
        
        # 启动处理循环
        self._start_processing_loop()
        
        print(f"⚡ 轻量级模型池初始化完成 (最大工作器: {max_workers})")
    
    def _create_workers(self):
        """创建工作器（不加载模型）"""
        for i in range(self.max_workers):
            worker = LazyModelWorker(i, self.model_path, self.config)
            self.workers.append(worker)
        
        print(f"📦 创建了 {len(self.workers)} 个懒加载工作器")
    
    def _start_processing_loop(self):
        """启动处理循环"""
        def processing_loop():
            while True:
                try:
                    # 获取请求
                    request = self.request_queue.get(timeout=1)
                    if request is None:  # 停止信号
                        break
                    
                    # 选择工作器
                    worker = self._select_worker()
                    if worker:
                        # 异步处理
                        future = self.executor.submit(self._process_with_worker, worker, request)
                    else:
                        # 没有可用工作器，返回错误
                        self._complete_request(request.request_id, {"error": "没有可用工作器"})
                
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"处理循环错误: {e}")
        
        thread = threading.Thread(target=processing_loop, daemon=True)
        thread.start()
    
    def _select_worker(self) -> Optional[LazyModelWorker]:
        """选择最佳工作器"""
        with self.worker_lock:
            # 优先选择已加载的工作器
            ready_workers = [w for w in self.workers if w.is_ready]
            if ready_workers:
                # 选择使用次数最少的
                return min(ready_workers, key=lambda w: w.request_count)
            
            # 选择未加载的工作器
            idle_workers = [w for w in self.workers if not w.is_loading and not w.is_ready]
            if idle_workers:
                return idle_workers[0]
            
            return None
    
    def _process_with_worker(self, worker: LazyModelWorker, request: LightRequest):
        """使用工作器处理请求"""
        try:
            result = worker.process_request(request)
            self._complete_request(request.request_id, result)
            
            # 更新统计
            if "error" not in result:
                self.stats["completed_requests"] += 1
            else:
                self.stats["failed_requests"] += 1
            
            # 更新平均处理时间
            if "processing_time" in result:
                total_completed = self.stats["completed_requests"]
                if total_completed > 0:
                    current_avg = self.stats["average_processing_time"]
                    new_avg = (current_avg * (total_completed - 1) + result["processing_time"]) / total_completed
                    self.stats["average_processing_time"] = new_avg
            
        except Exception as e:
            self._complete_request(request.request_id, {"error": str(e)})
            self.stats["failed_requests"] += 1
    
    def _complete_request(self, request_id: str, result: Dict[str, Any]):
        """完成请求"""
        if request_id in self.response_futures:
            future = self.response_futures[request_id]
            if not future.done():
                future.get_loop().call_soon_threadsafe(future.set_result, result)
    
    async def submit_request(self, prompt: str, max_tokens: int = 128, 
                           temperature: float = 0.3, priority: int = 1) -> Dict[str, Any]:
        """提交请求"""
        request_id = str(uuid.uuid4())
        
        # 创建 Future
        loop = asyncio.get_event_loop()
        future = loop.create_future()
        self.response_futures[request_id] = future
        
        # 创建请求
        request = LightRequest(
            request_id=request_id,
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            priority=priority
        )
        
        # 提交到队列
        self.request_queue.put(request)
        self.stats["total_requests"] += 1
        
        try:
            # 等待结果
            result = await asyncio.wait_for(future, timeout=30)
            return result
        except asyncio.TimeoutError:
            return {"error": "请求超时"}
        finally:
            # 清理
            if request_id in self.response_futures:
                del self.response_futures[request_id]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.worker_lock:
            active_workers = sum(1 for w in self.workers if w.is_ready)
            loading_workers = sum(1 for w in self.workers if w.is_loading)
        
        return {
            "pool_type": "lightweight",
            "max_workers": self.max_workers,
            "active_workers": active_workers,
            "loading_workers": loading_workers,
            "queue_size": self.request_queue.qsize(),
            "total_requests": self.stats["total_requests"],
            "completed_requests": self.stats["completed_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{(self.stats['completed_requests'] / max(1, self.stats['total_requests']) * 100):.1f}%",
            "average_processing_time": f"{self.stats['average_processing_time']:.2f}s",
            "pending_requests": len(self.response_futures)
        }
    
    def cleanup_idle_workers(self):
        """清理空闲工作器"""
        current_time = time.time()
        idle_threshold = 300  # 5分钟
        
        with self.worker_lock:
            for worker in self.workers:
                if (worker.is_ready and 
                    current_time - worker.last_used > idle_threshold):
                    worker.unload()
    
    def shutdown(self):
        """关闭模型池"""
        print("🛑 关闭轻量级模型池...")
        
        # 停止处理循环
        self.request_queue.put(None)
        
        # 卸载所有模型
        with self.worker_lock:
            for worker in self.workers:
                worker.unload()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        print("✅ 轻量级模型池已关闭")

# 全局轻量级模型池
lightweight_pool: Optional[LightweightModelPool] = None

def initialize_lightweight_pool(model_path: str, config: Dict[str, Any], max_workers: int = 2):
    """初始化轻量级模型池"""
    global lightweight_pool
    
    if lightweight_pool is None:
        print(f"⚡ 初始化轻量级模型池...")
        lightweight_pool = LightweightModelPool(model_path, config, max_workers)
        print("✅ 轻量级模型池初始化完成")
    
    return lightweight_pool

def get_lightweight_pool() -> Optional[LightweightModelPool]:
    """获取轻量级模型池"""
    return lightweight_pool
