"""
智能缓存系统 - 提升响应速度
"""

import hashlib
import json
import time
import threading
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import pickle
import os

class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
        # 缓存统计
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_requests": 0
        }
        
        # 启动清理线程
        self._start_cleanup_thread()
    
    def _generate_cache_key(self, prompt: str, **kwargs) -> str:
        """生成缓存键"""
        # 标准化输入
        normalized_prompt = prompt.strip().lower()
        
        # 包含重要参数
        cache_data = {
            "prompt": normalized_prompt,
            "max_tokens": kwargs.get("max_tokens", 128),
            "temperature": round(kwargs.get("temperature", 0.7), 2),
            "system_prompt": kwargs.get("system_prompt", "")
        }
        
        # 生成哈希
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def get(self, prompt: str, **kwargs) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        with self.lock:
            self.stats["total_requests"] += 1
            
            cache_key = self._generate_cache_key(prompt, **kwargs)
            
            if cache_key in self.cache:
                cache_entry = self.cache[cache_key]
                
                # 检查是否过期
                if time.time() - cache_entry["timestamp"] < self.ttl_seconds:
                    # 更新访问时间
                    self.access_times[cache_key] = time.time()
                    self.stats["hits"] += 1
                    
                    # 返回缓存结果（添加缓存标记）
                    result = cache_entry["data"].copy()
                    result["from_cache"] = True
                    result["cache_age"] = time.time() - cache_entry["timestamp"]
                    
                    return result
                else:
                    # 过期，删除
                    del self.cache[cache_key]
                    del self.access_times[cache_key]
            
            self.stats["misses"] += 1
            return None
    
    def put(self, prompt: str, result: Dict[str, Any], **kwargs):
        """存储缓存结果"""
        with self.lock:
            cache_key = self._generate_cache_key(prompt, **kwargs)
            
            # 检查缓存大小
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            # 存储结果
            self.cache[cache_key] = {
                "data": result.copy(),
                "timestamp": time.time()
            }
            self.access_times[cache_key] = time.time()
    
    def _evict_oldest(self):
        """淘汰最旧的缓存项"""
        if not self.access_times:
            return
        
        # 找到最旧的访问时间
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 删除最旧的项
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
        self.stats["evictions"] += 1
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_loop():
            while True:
                time.sleep(300)  # 每5分钟清理一次
                self._cleanup_expired()
        
        thread = threading.Thread(target=cleanup_loop, daemon=True)
        thread.start()
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        with self.lock:
            current_time = time.time()
            expired_keys = []
            
            for key, entry in self.cache.items():
                if current_time - entry["timestamp"] >= self.ttl_seconds:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            total_requests = self.stats["total_requests"]
            hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "cache_size": len(self.cache),
                "max_size": self.max_size,
                "hit_rate": f"{hit_rate:.1f}%",
                "hits": self.stats["hits"],
                "misses": self.stats["misses"],
                "evictions": self.stats["evictions"],
                "total_requests": total_requests
            }
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.stats = {"hits": 0, "misses": 0, "evictions": 0, "total_requests": 0}
    
    def save_to_disk(self, filepath: str):
        """保存缓存到磁盘"""
        with self.lock:
            cache_data = {
                "cache": self.cache,
                "access_times": self.access_times,
                "stats": self.stats,
                "saved_at": time.time()
            }
            
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'wb') as f:
                pickle.dump(cache_data, f)
    
    def load_from_disk(self, filepath: str) -> bool:
        """从磁盘加载缓存"""
        if not os.path.exists(filepath):
            return False
        
        try:
            with open(filepath, 'rb') as f:
                cache_data = pickle.load(f)
            
            with self.lock:
                # 检查缓存是否太旧（超过1天）
                if time.time() - cache_data.get("saved_at", 0) > 86400:
                    return False
                
                self.cache = cache_data.get("cache", {})
                self.access_times = cache_data.get("access_times", {})
                self.stats = cache_data.get("stats", {"hits": 0, "misses": 0, "evictions": 0, "total_requests": 0})
                
                # 清理过期项
                self._cleanup_expired()
                
            return True
            
        except Exception as e:
            print(f"加载缓存失败: {e}")
            return False

class ResponseCache:
    """响应缓存装饰器"""
    
    def __init__(self, cache: SmartCache):
        self.cache = cache
    
    def __call__(self, func):
        """缓存装饰器"""
        def wrapper(*args, **kwargs):
            # 提取关键参数
            if args and hasattr(args[0], '__dict__'):
                # 如果是类方法，跳过 self
                prompt = args[1] if len(args) > 1 else kwargs.get('prompt', '')
            else:
                prompt = args[0] if args else kwargs.get('prompt', '')
            
            # 尝试从缓存获取
            cached_result = self.cache.get(prompt, **kwargs)
            if cached_result:
                return cached_result
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            if isinstance(result, dict) and not result.get("error"):
                self.cache.put(prompt, result, **kwargs)
            
            return result
        
        return wrapper

# 全局缓存实例
global_cache = SmartCache(max_size=2000, ttl_seconds=7200)  # 2小时TTL

# 在应用启动时加载缓存
cache_file = "cache/response_cache.pkl"
if global_cache.load_from_disk(cache_file):
    print("✅ 缓存已从磁盘加载")

# 定期保存缓存
def save_cache_periodically():
    """定期保存缓存"""
    import atexit
    
    def save_cache():
        global_cache.save_to_disk(cache_file)
        print("💾 缓存已保存到磁盘")
    
    # 程序退出时保存
    atexit.register(save_cache)
    
    # 定期保存
    def periodic_save():
        while True:
            time.sleep(1800)  # 每30分钟保存一次
            save_cache()
    
    thread = threading.Thread(target=periodic_save, daemon=True)
    thread.start()

# 启动定期保存
save_cache_periodically()
