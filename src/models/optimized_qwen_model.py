"""
优化版 Qwen 模型 - 支持缓存和并行处理
"""

import os
import json
import time
import asyncio
import logging
from typing import Dict, Any, Optional

# 导入缓存和并行处理模块
try:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from cache.smart_cache import global_cache, ResponseCache
    from parallel.lightweight_pool import initialize_lightweight_pool, get_lightweight_pool
    CACHE_AVAILABLE = True
    PARALLEL_AVAILABLE = True
    print("✅ 使用轻量级并行处理池")
except ImportError as e:
    CACHE_AVAILABLE = False
    PARALLEL_AVAILABLE = False
    print(f"⚠️  缓存或并行处理模块不可用: {e}")

try:
    from llama_cpp import Llama
except ImportError:
    Llama = None

logger = logging.getLogger(__name__)

class OptimizedQwenModel:
    """优化版 Qwen 模型 - 支持缓存和并行处理"""
    
    def __init__(self, config_path: str = "models/model_config.json", enable_cache: bool = True, enable_parallel: bool = True):
        # 路径处理
        if not os.path.isabs(config_path):
            current_file = os.path.abspath(__file__)
            src_dir = os.path.dirname(os.path.dirname(current_file))
            project_root = os.path.dirname(src_dir)
            self.config_path = os.path.join(project_root, config_path)
        else:
            self.config_path = config_path
        
        self.config: Dict[str, Any] = {}
        self.is_loaded = False
        
        # 功能开关
        self.enable_cache = enable_cache and CACHE_AVAILABLE
        self.enable_parallel = enable_parallel and PARALLEL_AVAILABLE
        
        # 传统模型（作为后备）
        self.model: Optional[Llama] = None
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "parallel_requests": 0,
            "fallback_requests": 0,
            "average_response_time": 0.0
        }
        
        print(f"🚀 优化模型初始化:")
        print(f"   缓存: {'✅' if self.enable_cache else '❌'}")
        print(f"   并行: {'✅' if self.enable_parallel else '❌'}")
    
    async def load_model(self):
        """加载模型"""
        try:
            await self._load_config()
            
            model_path = self.config.get("model_path")
            if not model_path or not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            logger.info(f"📥 加载优化模型: {os.path.basename(model_path)}")
            
            # 初始化轻量级并行处理池
            if self.enable_parallel:
                try:
                    cpu_count = os.cpu_count() or 4
                    pool_size = min(2, max(1, cpu_count // 4))  # 根据CPU核心数确定池大小
                    initialize_lightweight_pool(model_path, self.config, pool_size)
                    logger.info(f"✅ 轻量级并行处理池已初始化 (大小: {pool_size})")
                except Exception as e:
                    logger.warning(f"⚠️  轻量级并行处理池初始化失败: {e}")
                    self.enable_parallel = False
            
            # 加载传统模型作为后备
            if not self.enable_parallel:
                loop = asyncio.get_event_loop()
                self.model = await loop.run_in_executor(None, self._load_model_sync)
                logger.info("✅ 传统模型已加载")
            
            self.is_loaded = True
            logger.info("🎉 优化模型加载完成")
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def _load_model_sync(self) -> Llama:
        """同步加载传统模型"""
        if Llama is None:
            raise ImportError("llama-cpp-python 未安装")
        
        return Llama(
            model_path=self.config["model_path"],
            n_ctx=self.config.get("context_length", 512),
            n_threads=self.config.get("threads", 4),
            n_batch=self.config.get("n_batch", 256),
            use_mmap=self.config.get("use_mmap", True),
            use_mlock=self.config.get("use_mlock", False),
            verbose=False
        )
    
    async def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                
                # 修正模型路径
                model_path = self.config.get("model_path", "")
                if model_path and not os.path.isabs(model_path):
                    current_file = os.path.abspath(__file__)
                    src_dir = os.path.dirname(os.path.dirname(current_file))
                    project_root = os.path.dirname(src_dir)
                    self.config["model_path"] = os.path.join(project_root, model_path)
            else:
                # 默认配置
                current_file = os.path.abspath(__file__)
                src_dir = os.path.dirname(os.path.dirname(current_file))
                project_root = os.path.dirname(src_dir)
                self.config = {
                    "model_path": os.path.join(project_root, "models/qwen1_5-4b-chat-q4_k_m.gguf"),
                    "context_length": 512,
                    "max_tokens": 128,
                    "temperature": 0.3,
                    "top_p": 0.8,
                    "repeat_penalty": 1.02,
                    "threads": 4,
                    "gpu_layers": 0,
                    "n_batch": 256,
                    "use_mmap": True,
                    "use_mlock": False
                }
                logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    async def generate(
        self,
        prompt: str,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """优化的文本生成"""
        if not self.is_loaded:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        # 参数处理
        temp = temperature or self.config.get("temperature", 0.3)
        max_tok = min(max_tokens or self.config.get("max_tokens", 128), 256)  # 限制最大token
        
        # 构建提示
        full_prompt = self._build_prompt(prompt, system_prompt)
        
        try:
            # 1. 尝试缓存
            if self.enable_cache:
                cached_result = global_cache.get(
                    full_prompt,
                    max_tokens=max_tok,
                    temperature=temp,
                    system_prompt=system_prompt or ""
                )
                
                if cached_result:
                    self.stats["cache_hits"] += 1
                    processing_time = time.time() - start_time
                    
                    # 更新统计
                    self._update_stats(processing_time)
                    
                    logger.info(f"💾 缓存命中 ({processing_time:.2f}s)")
                    return cached_result
            
            # 2. 尝试并行处理
            if self.enable_parallel:
                model_pool = get_lightweight_pool()
                if model_pool:
                    try:
                        result = await model_pool.submit_request(
                            prompt=full_prompt,
                            max_tokens=max_tok,
                            temperature=temp,
                            priority=2  # 中等优先级
                        )
                        
                        if "error" not in result:
                            self.stats["parallel_requests"] += 1
                            processing_time = time.time() - start_time
                            
                            # 格式化结果
                            formatted_result = {
                                "text": result.get("text", ""),
                                "model": result.get("model", "Qwen-Parallel"),
                                "tokens_used": result.get("tokens_used", 0),
                                "processing_time": round(processing_time, 2),
                                "worker_id": result.get("worker_id", 0)
                            }
                            
                            # 存储到缓存
                            if self.enable_cache:
                                global_cache.put(full_prompt, formatted_result, 
                                               max_tokens=max_tok, temperature=temp, 
                                               system_prompt=system_prompt or "")
                            
                            self._update_stats(processing_time)
                            logger.info(f"⚡ 并行处理完成 ({processing_time:.2f}s)")
                            return formatted_result
                    
                    except Exception as e:
                        logger.warning(f"并行处理失败，使用后备模式: {e}")
            
            # 3. 后备：传统模型
            if self.model:
                self.stats["fallback_requests"] += 1
                
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None, self._generate_sync, full_prompt, temp, max_tok
                )
                
                processing_time = time.time() - start_time
                
                formatted_result = {
                    "text": result["choices"][0]["text"].strip(),
                    "model": self.config.get("model_name", "Qwen-Fallback"),
                    "tokens_used": result["usage"]["total_tokens"],
                    "processing_time": round(processing_time, 2)
                }
                
                # 存储到缓存
                if self.enable_cache:
                    global_cache.put(full_prompt, formatted_result,
                                   max_tokens=max_tok, temperature=temp,
                                   system_prompt=system_prompt or "")
                
                self._update_stats(processing_time)
                logger.info(f"🔄 后备模式完成 ({processing_time:.2f}s)")
                return formatted_result
            
            else:
                raise RuntimeError("所有处理方式都不可用")
        
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time)
            logger.error(f"文本生成失败: {e}")
            raise
    
    def _generate_sync(self, prompt: str, temperature: float, max_tokens: int):
        """同步生成文本（后备模式）"""
        return self.model(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=self.config.get("top_p", 0.8),
            repeat_penalty=self.config.get("repeat_penalty", 1.02),
            stop=["\\n\\n", "。", ".", "!", "?", "<|endoftext|>", "<|im_end|>"]
        )
    
    def _build_prompt(self, user_input: str, system_prompt: Optional[str] = None) -> str:
        """构建 Qwen 格式的提示"""
        if system_prompt:
            return f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
        else:
            return f"<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
    
    def _update_stats(self, processing_time: float):
        """更新统计信息"""
        total = self.stats["total_requests"]
        current_avg = self.stats["average_response_time"]
        new_avg = (current_avg * (total - 1) + processing_time) / total
        self.stats["average_response_time"] = new_avg
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = self.stats["total_requests"]
        
        stats = {
            "total_requests": total,
            "cache_hits": self.stats["cache_hits"],
            "parallel_requests": self.stats["parallel_requests"],
            "fallback_requests": self.stats["fallback_requests"],
            "average_response_time": f"{self.stats['average_response_time']:.2f}s",
            "cache_hit_rate": f"{(self.stats['cache_hits'] / max(1, total) * 100):.1f}%",
            "parallel_usage": f"{(self.stats['parallel_requests'] / max(1, total) * 100):.1f}%"
        }
        
        # 添加缓存统计
        if self.enable_cache:
            cache_stats = global_cache.get_stats()
            stats["cache_stats"] = cache_stats
        
        # 添加并行处理统计
        if self.enable_parallel:
            model_pool = get_lightweight_pool()
            if model_pool:
                pool_stats = model_pool.get_stats()
                stats["pool_stats"] = pool_stats
        
        return stats
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
        
        # 清理并行处理池
        if self.enable_parallel:
            model_pool = get_lightweight_pool()
            if model_pool:
                model_pool.shutdown()
        
        self.is_loaded = False
        logger.info("🧹 优化模型资源已清理")
