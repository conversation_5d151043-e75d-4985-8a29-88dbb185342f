
import os
import time
from typing import Optional, Dict, Any

class MinimalQwenModel:
    """最小化 Qwen 模型 - 专注启动速度"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        self.config = {}
        
    async def load_model(self):
        """快速加载模型"""
        print("⚡ 快速加载模式...")
        
        # 模拟快速加载
        import asyncio
        await asyncio.sleep(0.1)  # 最小延迟
        
        # 延迟导入
        try:
            from llama_cpp import Llama
            
            # 读取配置
            config_path = "models/model_config.json"
            if os.path.exists(config_path):
                import json
                with open(config_path, 'r') as f:
                    self.config = json.load(f)
            
            model_path = self.config.get("model_path", "")
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            print(f"📥 加载模型: {os.path.basename(model_path)}")
            
            # 最小配置加载
            self.model = Llama(
                model_path=model_path,
                n_ctx=256,           # 最小上下文
                n_threads=2,         # 最少线程
                n_batch=32,          # 最小批处理
                use_mmap=True,
                use_mlock=False,
                verbose=False
            )
            
            self.is_loaded = True
            print("✅ 快速加载完成")
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            raise
    
    async def generate(self, prompt: str, temperature: float = 0.2, 
                      max_tokens: int = 64, system_prompt: str = None) -> Dict[str, Any]:
        """快速生成"""
        if not self.is_loaded:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 构建提示
        if system_prompt:
            full_prompt = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
        else:
            full_prompt = f"<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
        
        # 快速生成
        result = self.model(
            full_prompt,
            max_tokens=min(max_tokens, 64),  # 限制长度
            temperature=temperature,
            stop=["\n", "。", ".", "!", "?"],  # 早停
            echo=False
        )
        
        processing_time = time.time() - start_time
        
        return {
            "text": result["choices"][0]["text"].strip(),
            "model": "Qwen-Fast",
            "tokens_used": result["usage"]["total_tokens"],
            "processing_time": round(processing_time, 2)
        }
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_name": "Qwen-Fast",
            "is_loaded": self.is_loaded,
            "mode": "minimal"
        }
