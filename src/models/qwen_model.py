"""
Qwen 模型管理类
负责模型加载、推理和资源管理
"""

import os
import json
import time
import asyncio
import logging
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from llama_cpp import Llama
except ImportError:
    Llama = None

logger = logging.getLogger(__name__)

class QwenModel:
    """Qwen 模型管理类"""
    
    def __init__(self, config_path: str = "models/model_config.json"):
        # 确保路径是相对于项目根目录的
        if not os.path.isabs(config_path):
            # 获取项目根目录（src 的上级目录）
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.config_path = os.path.join(project_root, config_path)
        else:
            self.config_path = config_path

        self.model: Optional[Llama] = None
        self.config: Dict[str, Any] = {}
        self.is_loaded = False
        
    async def load_model(self):
        """异步加载模型"""
        try:
            # 读取配置
            await self._load_config()
            
            # 检查模型文件
            model_path = self.config.get("model_path")
            if not model_path or not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")
            
            logger.info(f"📥 加载模型: {model_path}")
            
            # 在线程池中加载模型（避免阻塞）
            loop = asyncio.get_event_loop()
            self.model = await loop.run_in_executor(
                None, self._load_model_sync
            )
            
            self.is_loaded = True
            logger.info("✅ 模型加载完成")
            
        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def _load_model_sync(self) -> Llama:
        """同步加载模型"""
        if Llama is None:
            raise ImportError("llama-cpp-python 未安装")
        
        return Llama(
            model_path=self.config["model_path"],
            n_ctx=self.config.get("context_length", 32768),
            n_threads=self.config.get("threads", 8),
            n_gpu_layers=self.config.get("gpu_layers", 0),
            verbose=False
        )
    
    async def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)

                # 修正模型路径为绝对路径
                model_path = self.config.get("model_path", "")
                if model_path and not os.path.isabs(model_path):
                    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    self.config["model_path"] = os.path.join(project_root, model_path)
            else:
                # 默认配置
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                self.config = {
                    "model_path": os.path.join(project_root, "models/qwen1_5-4b-chat-q4_k_m.gguf"),
                    "context_length": 32768,
                    "max_tokens": 2048,
                    "temperature": 0.7,
                    "top_p": 0.8,
                    "repeat_penalty": 1.1,
                    "threads": 8,
                    "gpu_layers": 0
                }
                logger.warning("配置文件不存在，使用默认配置")
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    async def generate(
        self,
        prompt: str,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成文本"""
        if not self.is_loaded or not self.model:
            raise RuntimeError("模型未加载")
        
        # 构建完整提示
        full_prompt = self._build_prompt(prompt, system_prompt)
        
        # 参数设置
        temp = temperature or self.config.get("temperature", 0.7)
        max_tok = max_tokens or self.config.get("max_tokens", 1024)
        
        start_time = time.time()
        
        try:
            # 在线程池中执行推理
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, self._generate_sync, full_prompt, temp, max_tok
            )
            
            processing_time = time.time() - start_time
            
            return {
                "text": result["choices"][0]["text"].strip(),
                "model": self.config.get("model_name", "Qwen"),
                "tokens_used": result["usage"]["total_tokens"],
                "processing_time": round(processing_time, 2)
            }
            
        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            raise
    
    def _generate_sync(self, prompt: str, temperature: float, max_tokens: int):
        """同步生成文本"""
        return self.model(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=self.config.get("top_p", 0.8),
            repeat_penalty=self.config.get("repeat_penalty", 1.1),
            stop=["<|endoftext|>", "<|im_end|>"]
        )
    
    def _build_prompt(self, user_input: str, system_prompt: Optional[str] = None) -> str:
        """构建 Qwen 格式的提示"""
        if system_prompt:
            return f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
        else:
            return f"<|im_start|>user\n{user_input}<|im_end|>\n<|im_start|>assistant\n"
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
        logger.info("🧹 模型资源已清理")
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.config.get("model_name", "Unknown"),
            "model_path": self.config.get("model_path", ""),
            "is_loaded": self.is_loaded,
            "context_length": self.config.get("context_length", 0),
            "config": self.config
        }
