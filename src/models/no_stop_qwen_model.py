
"""
无停止词模型 - 专门用于长文本生成
"""

import os
import time
import asyncio
from typing import Optional, Dict, Any

try:
    from llama_cpp import Llama
except ImportError:
    Llama = None

class NoStopQwenModel:
    """无停止词 Qwen 模型 - 专门用于长文本"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        self.config = {}
        
    async def load_model(self):
        """加载无停止词模型"""
        print("🚀 加载无停止词长文本模型...")
        
        # 读取配置
        config_path = "models/model_config.json"
        if os.path.exists(config_path):
            import json
            with open(config_path, 'r') as f:
                self.config = json.load(f)
        
        model_path = self.config.get("model_path", "")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        print(f"📥 加载模型: {os.path.basename(model_path)}")
        
        # 在线程池中加载
        loop = asyncio.get_event_loop()
        self.model = await loop.run_in_executor(None, self._load_model_sync)
        
        self.is_loaded = True
        print("✅ 无停止词模型加载完成")
    
    def _load_model_sync(self):
        """同步加载模型"""
        if Llama is None:
            raise ImportError("llama-cpp-python 未安装")
        
        return Llama(
            model_path=self.config["model_path"],
            n_ctx=self.config.get("context_length", 8192),      # 大上下文
            n_threads=self.config.get("threads", os.cpu_count() or 4),
            n_batch=self.config.get("n_batch", 1024),           # 大批处理
            use_mmap=self.config.get("use_mmap", True),
            use_mlock=self.config.get("use_mlock", False),
            verbose=False
        )
    
    async def generate(self, prompt: str, temperature: float = 0.9, 
                      max_tokens: int = 4096, system_prompt: str = None) -> Dict[str, Any]:
        """生成长文本（无停止词）"""
        if not self.is_loaded:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        # 构建提示
        if system_prompt:
            full_prompt = f"<|im_start|>system\n{system_prompt}<|im_end|>\n<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
        else:
            full_prompt = f"<|im_start|>user\n{prompt}<|im_end|>\n<|im_start|>assistant\n"
        
        # 在线程池中生成
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None, self._generate_sync, full_prompt, temperature, max_tokens
        )
        
        processing_time = time.time() - start_time
        
        return {
            "text": result["choices"][0]["text"].strip(),
            "model": "Qwen-NoStop",
            "tokens_used": result["usage"]["total_tokens"],
            "processing_time": round(processing_time, 2)
        }
    
    def _generate_sync(self, prompt: str, temperature: float, max_tokens: int):
        """同步生成（无停止词）"""
        return self.model(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=self.config.get("top_p", 0.95),
            repeat_penalty=self.config.get("repeat_penalty", 1.15),
            stop=[],  # 无停止词！
            echo=False
        )
    
    def cleanup(self):
        """清理资源"""
        if self.model:
            del self.model
            self.model = None
        self.is_loaded = False
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_name": "Qwen-NoStop",
            "is_loaded": self.is_loaded,
            "mode": "long_text_no_stop"
        }
