"""
教育系统集成示例
支持 Mo<PERSON><PERSON>、<PERSON><PERSON>、钉钉教育等平台
"""

import httpx
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

class EducationIntegration:
    """教育系统集成类"""
    
    def __init__(self, qwen_api_url: str = "http://localhost:8000"):
        self.qwen_api_url = qwen_api_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def grade_assignment(
        self,
        assignment_text: str,
        student_answer: str,
        subject: str,
        grade_level: str = "高中"
    ) -> Dict[str, Any]:
        """作业批改"""
        
        system_prompt = f"""
        你是一位专业的{subject}老师，正在批改{grade_level}学生的作业。
        
        作业题目：{assignment_text}
        学生答案：{student_answer}
        
        请提供：
        1. 分数（0-100分）
        2. 详细评语
        3. 答案的优点
        4. 需要改进的地方
        5. 学习建议
        6. 参考答案（如果适用）
        
        请以教育性和鼓励性的语调进行评价。
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": student_answer,
                    "system_prompt": system_prompt,
                    "temperature": 0.3,
                    "max_tokens": 1000
                }
            )
            
            result = response.json()
            
            return {
                "subject": subject,
                "grade_level": grade_level,
                "grading_result": result["response"],
                "processing_time": result["processing_time"],
                "graded_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "subject": subject,
                "graded_at": datetime.now().isoformat()
            }
    
    async def generate_quiz_questions(
        self,
        topic: str,
        difficulty: str = "中等",
        question_count: int = 5,
        question_type: str = "选择题"
    ) -> Dict[str, Any]:
        """生成测验题目"""
        
        system_prompt = f"""
        请为"{topic}"主题生成{question_count}道{difficulty}难度的{question_type}。
        
        要求：
        1. 题目要有教育意义和实用性
        2. 选择题需要4个选项，标明正确答案
        3. 包含详细的解析
        4. 难度适合学生水平
        
        请以结构化格式返回。
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": f"主题：{topic}",
                    "system_prompt": system_prompt,
                    "temperature": 0.5,
                    "max_tokens": 1500
                }
            )
            
            result = response.json()
            
            return {
                "topic": topic,
                "difficulty": difficulty,
                "question_count": question_count,
                "question_type": question_type,
                "questions": result["response"],
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    async def provide_learning_feedback(
        self,
        student_performance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提供学习反馈"""
        
        performance_text = json.dumps(student_performance, ensure_ascii=False)
        
        system_prompt = """
        基于以下学生学习表现数据，提供个性化的学习反馈：
        1. 学习进度评估
        2. 强项和弱项分析
        3. 具体改进建议
        4. 推荐学习资源
        5. 下一步学习计划
        
        请以鼓励和建设性的语调提供反馈。
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": performance_text,
                    "system_prompt": system_prompt,
                    "temperature": 0.4,
                    "max_tokens": 1000
                }
            )
            
            result = response.json()
            
            return {
                "feedback": result["response"],
                "student_id": student_performance.get("student_id"),
                "feedback_date": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# Moodle 集成示例
class MoodleIntegration(EducationIntegration):
    """Moodle LMS 集成"""
    
    def __init__(self, moodle_url: str, token: str):
        super().__init__()
        self.moodle_url = moodle_url
        self.token = token
    
    async def get_course_assignments(self, course_id: int) -> List[Dict[str, Any]]:
        """获取课程作业"""
        
        url = f"{self.moodle_url}/webservice/rest/server.php"
        params = {
            "wstoken": self.token,
            "wsfunction": "mod_assign_get_assignments",
            "moodlewsrestformat": "json",
            "courseids[0]": course_id
        }
        
        try:
            response = await self.client.get(url, params=params)
            data = response.json()
            
            assignments = []
            for course in data.get("courses", []):
                for assignment in course.get("assignments", []):
                    assignments.append({
                        "id": assignment["id"],
                        "name": assignment["name"],
                        "intro": assignment["intro"],
                        "duedate": assignment.get("duedate", 0)
                    })
            
            return assignments
            
        except Exception as e:
            return [{"error": str(e)}]
    
    async def process_assignment_submissions(
        self,
        assignment_id: int,
        subject: str
    ) -> List[Dict[str, Any]]:
        """处理作业提交"""
        
        # 1. 获取作业提交
        submissions = await self.get_assignment_submissions(assignment_id)
        
        # 2. 批改每个提交
        results = []
        for submission in submissions:
            if submission.get("onlinetext"):
                grading = await self.grade_assignment(
                    assignment_text=submission.get("assignment_name", ""),
                    student_answer=submission["onlinetext"],
                    subject=subject
                )
                
                results.append({
                    "student_id": submission["userid"],
                    "submission_id": submission["id"],
                    "grading": grading
                })
        
        return results
    
    async def get_assignment_submissions(self, assignment_id: int):
        """获取作业提交（示例实现）"""
        # 这里应该调用 Moodle API 获取实际提交
        return [
            {
                "id": 1,
                "userid": 123,
                "assignment_name": "数学作业",
                "onlinetext": "学生的答案内容..."
            }
        ]

# 智能答疑系统
class IntelligentQA:
    """智能答疑系统"""
    
    def __init__(self, qwen_api_url: str = "http://localhost:8000"):
        self.qwen_api_url = qwen_api_url
        self.client = httpx.AsyncClient(timeout=60.0)
        self.knowledge_base = {}
    
    async def answer_student_question(
        self,
        question: str,
        subject: str,
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """回答学生问题"""
        
        system_prompt = f"""
        你是一位专业的{subject}老师，正在回答学生的问题。
        
        请提供：
        1. 清晰准确的答案
        2. 详细的解释过程
        3. 相关的例子或类比
        4. 扩展知识点
        5. 学习建议
        
        请用学生容易理解的语言，循序渐进地解释。
        """
        
        full_question = question
        if context:
            full_question = f"背景：{context}\n问题：{question}"
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": full_question,
                    "system_prompt": system_prompt,
                    "temperature": 0.3,
                    "max_tokens": 1200
                }
            )
            
            result = response.json()
            
            return {
                "question": question,
                "subject": subject,
                "answer": result["response"],
                "confidence": "高",  # 可以基于模型输出计算置信度
                "answered_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "question": question,
                "error": str(e),
                "answered_at": datetime.now().isoformat()
            }
    
    async def generate_study_plan(
        self,
        student_profile: Dict[str, Any],
        learning_goals: List[str]
    ) -> Dict[str, Any]:
        """生成学习计划"""
        
        profile_text = json.dumps(student_profile, ensure_ascii=False)
        goals_text = "、".join(learning_goals)
        
        system_prompt = f"""
        基于学生档案和学习目标，制定个性化学习计划：
        
        学生档案：{profile_text}
        学习目标：{goals_text}
        
        请提供：
        1. 学习路径规划
        2. 时间安排建议
        3. 学习方法推荐
        4. 阶段性目标
        5. 评估方式
        6. 资源推荐
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": f"目标：{goals_text}",
                    "system_prompt": system_prompt,
                    "temperature": 0.4,
                    "max_tokens": 1500
                }
            )
            
            result = response.json()
            
            return {
                "student_profile": student_profile,
                "learning_goals": learning_goals,
                "study_plan": result["response"],
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# 使用示例
async def example_usage():
    """使用示例"""
    
    # 1. 作业批改
    edu = EducationIntegration()
    
    grading_result = await edu.grade_assignment(
        assignment_text="请解释牛顿第一定律",
        student_answer="牛顿第一定律说的是物体在没有外力作用时会保持静止或匀速直线运动状态。",
        subject="物理",
        grade_level="高中"
    )
    
    print("批改结果:", grading_result)
    
    # 2. 生成测验题
    quiz_result = await edu.generate_quiz_questions(
        topic="二次函数",
        difficulty="中等",
        question_count=3,
        question_type="选择题"
    )
    
    print("测验题目:", quiz_result)
    
    # 3. 智能答疑
    qa = IntelligentQA()
    
    answer = await qa.answer_student_question(
        question="为什么水会结冰？",
        subject="化学",
        context="我们正在学习物质的三态变化"
    )
    
    print("答疑结果:", answer)

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
