"""
稳定的视频处理和字幕提取
"""

import os
import subprocess
import tempfile
import asyncio
import time
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import shutil

# 设置日志
logger = logging.getLogger(__name__)

class VideoProcessor:
    """视频处理器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="video_processing_")
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']
        
    def check_dependencies(self) -> Dict[str, bool]:
        """检查依赖工具"""
        dependencies = {}
        
        # 检查 ffmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            dependencies['ffmpeg'] = result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            dependencies['ffmpeg'] = False
        
        # 检查 whisper (可选)
        try:
            import whisper
            dependencies['whisper'] = True
        except ImportError:
            dependencies['whisper'] = False
        
        return dependencies
    
    async def extract_audio_from_video(self, video_path: str) -> str:
        """从视频中提取音频"""
        try:
            video_file = Path(video_path)
            if not video_file.exists():
                raise FileNotFoundError(f"视频文件不存在: {video_path}")
            
            if video_file.suffix.lower() not in self.supported_formats:
                raise ValueError(f"不支持的视频格式: {video_file.suffix}")
            
            # 生成音频文件路径
            audio_path = os.path.join(self.temp_dir, f"{video_file.stem}.wav")
            
            # 使用 ffmpeg 提取音频
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn',  # 不要视频
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出文件
                audio_path
            ]
            
            print(f"🎵 正在提取音频: {video_file.name}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "未知错误"
                raise RuntimeError(f"音频提取失败: {error_msg}")
            
            if not os.path.exists(audio_path):
                raise RuntimeError("音频文件未生成")
            
            print(f"✅ 音频提取完成: {audio_path}")
            return audio_path
            
        except Exception as e:
            raise RuntimeError(f"音频提取失败: {str(e)}")
    
    async def transcribe_audio_with_whisper(self, audio_path: str, language: str = "auto") -> Dict[str, Any]:
        """使用 Whisper 转录音频"""
        try:
            import whisper
            
            print(f"🎤 正在转录音频...")
            
            # 加载 Whisper 模型
            model = whisper.load_model("base")  # 可选: tiny, base, small, medium, large
            
            # 转录音频
            result = model.transcribe(
                audio_path,
                language=None if language == "auto" else language,
                task="transcribe"
            )
            
            # 提取字幕信息
            segments = []
            for segment in result["segments"]:
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip()
                })
            
            return {
                "language": result["language"],
                "segments": segments,
                "full_text": result["text"]
            }
            
        except ImportError:
            raise RuntimeError("Whisper 未安装。请运行: pip install openai-whisper")
        except Exception as e:
            raise RuntimeError(f"音频转录失败: {str(e)}")
    
    def generate_srt_from_segments(self, segments: list) -> str:
        """从转录片段生成SRT字幕"""
        srt_content = []
        
        for i, segment in enumerate(segments, 1):
            start_time = self._seconds_to_srt_time(segment["start"])
            end_time = self._seconds_to_srt_time(segment["end"])
            text = segment["text"]
            
            srt_block = f"""{i}
{start_time} --> {end_time}
{text}"""
            srt_content.append(srt_block)
        
        return '\n\n'.join(srt_content)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def extract_existing_subtitles(self, video_path: str) -> Optional[str]:
        """提取视频中已有的字幕"""
        try:
            subtitle_path = os.path.join(self.temp_dir, "extracted_subtitles.srt")
            
            cmd = [
                'ffmpeg', '-i', video_path,
                '-map', '0:s:0',  # 选择第一个字幕流
                '-c:s', 'srt',    # 转换为SRT格式
                '-y',
                subtitle_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and os.path.exists(subtitle_path):
                with open(subtitle_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            return None
            
        except Exception:
            return None
    
    async def process_video_complete(self, video_path: str, target_language: str = "zh", 
                                   extract_method: str = "whisper") -> Dict[str, Any]:
        """完整的视频处理流程"""
        try:
            video_file = Path(video_path)
            
            # 检查依赖
            deps = self.check_dependencies()
            if not deps['ffmpeg']:
                raise RuntimeError("ffmpeg 未安装或不可用")
            
            result = {
                "video_file": video_file.name,
                "target_language": target_language,
                "processing_steps": []
            }
            
            # 步骤1: 尝试提取已有字幕
            print("🔍 检查视频中的现有字幕...")
            existing_subtitles = await self.extract_existing_subtitles(video_path)
            
            if existing_subtitles:
                print("✅ 发现现有字幕，将直接翻译")
                result["processing_steps"].append("提取现有字幕")
                result["original_subtitles"] = existing_subtitles
                
                # 翻译现有字幕
                from subtitle_translation import SubtitleTranslator
                translator = SubtitleTranslator()
                
                translation_result = await translator.translate_srt_file(
                    existing_subtitles, target_language
                )
                
                await translator.close()
                
                if "error" not in translation_result:
                    result["translated_subtitles"] = translation_result["translated_srt"]
                    result["processing_steps"].append("翻译字幕")
                    result["success"] = True
                    return result
            
            # 步骤2: 如果没有现有字幕，使用语音识别
            if extract_method == "whisper" and deps['whisper']:
                print("🎵 提取音频进行语音识别...")
                result["processing_steps"].append("提取音频")
                
                # 提取音频
                audio_path = await self.extract_audio_from_video(video_path)
                
                # 语音识别
                print("🎤 进行语音识别...")
                result["processing_steps"].append("语音识别")
                
                transcription = await self.transcribe_audio_with_whisper(audio_path)
                
                # 生成字幕
                original_srt = self.generate_srt_from_segments(transcription["segments"])
                result["original_subtitles"] = original_srt
                result["detected_language"] = transcription["language"]
                result["processing_steps"].append("生成字幕")
                
                # 翻译字幕
                print("🌐 翻译字幕...")
                from subtitle_translation import SubtitleTranslator
                translator = SubtitleTranslator()
                
                translation_result = await translator.translate_srt_file(
                    original_srt, target_language, transcription["language"]
                )
                
                await translator.close()
                
                if "error" not in translation_result:
                    result["translated_subtitles"] = translation_result["translated_srt"]
                    result["processing_steps"].append("翻译字幕")
                    result["success"] = True
                else:
                    result["error"] = translation_result["error"]
                    result["success"] = False
                
                return result
            
            else:
                raise RuntimeError("无法处理视频：缺少必要的依赖或不支持的提取方法")
            
        except Exception as e:
            return {
                "error": str(e),
                "success": False,
                "video_file": Path(video_path).name if video_path else "unknown"
            }
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print(f"🧹 清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"⚠️  清理临时文件失败: {e}")
    
    def __del__(self):
        """析构函数，自动清理"""
        self.cleanup()

class VideoUploadHandler:
    """视频上传处理器"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        self.max_file_size = 500 * 1024 * 1024  # 500MB
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        try:
            # 检查文件大小
            if len(file_content) > self.max_file_size:
                raise ValueError(f"文件太大，最大支持 {self.max_file_size // (1024*1024)}MB")
            
            # 生成安全的文件名
            safe_filename = self._sanitize_filename(filename)
            file_path = self.upload_dir / safe_filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            print(f"📁 文件已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            raise RuntimeError(f"文件保存失败: {str(e)}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名"""
        import re
        import uuid
        
        # 保留扩展名
        name, ext = os.path.splitext(filename)
        
        # 清理文件名
        safe_name = re.sub(r'[^\w\-_.]', '_', name)
        
        # 添加时间戳避免冲突
        timestamp = str(int(time.time()))
        
        return f"{safe_name}_{timestamp}{ext}"
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """清理旧文件"""
        try:
            import time
            current_time = time.time()
            
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_hours * 3600:
                        file_path.unlink()
                        print(f"🗑️  删除旧文件: {file_path.name}")
                        
        except Exception as e:
            print(f"⚠️  清理旧文件失败: {e}")
