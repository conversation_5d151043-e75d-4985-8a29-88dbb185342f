"""
轻量级视频翻译器 - 避免内存问题
使用外部翻译API而不是本地模型
"""

import os
import subprocess
import tempfile
import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import shutil
import json
import requests

logger = logging.getLogger(__name__)

class LightweightVideoTranslator:
    """轻量级视频翻译器"""
    
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="lightweight_video_")
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']
        self.max_file_size = 200 * 1024 * 1024  # 200MB (减少限制)
        
    def check_dependencies(self) -> Dict[str, bool]:
        """检查依赖 - 只需要FFmpeg和基础包"""
        deps = {}
        
        # 检查 ffmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            deps['ffmpeg'] = result.returncode == 0
        except:
            deps['ffmpeg'] = False
        
        # 检查 whisper (可选)
        try:
            import whisper
            deps['whisper'] = True
        except ImportError:
            deps['whisper'] = False
        
        return deps
    
    async def extract_audio_from_video(self, video_path: str) -> str:
        """从视频提取音频"""
        try:
            audio_path = os.path.join(self.temp_dir, "extracted_audio.wav")
            
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn',  # 不要视频流
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率16kHz
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出
                audio_path
            ]
            
            logger.info(f"提取音频: {' '.join(cmd)}")
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=300  # 5分钟超时
            )
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "未知错误"
                raise RuntimeError(f"音频提取失败: {error_msg}")
            
            if not os.path.exists(audio_path):
                raise RuntimeError("音频文件未生成")
            
            logger.info(f"音频提取成功: {audio_path}")
            return audio_path
            
        except Exception as e:
            raise RuntimeError(f"音频提取失败: {str(e)}")
    
    async def transcribe_with_whisper_safe(self, audio_path: str, language: str = None) -> Dict[str, Any]:
        """安全的Whisper语音识别 - 使用最小模型"""
        try:
            import whisper
            
            logger.info("开始语音识别...")
            
            # 在线程池中运行Whisper（避免阻塞）
            loop = asyncio.get_event_loop()
            
            def run_whisper():
                try:
                    # 使用最小的模型减少内存使用
                    model = whisper.load_model("tiny")  # 只使用tiny模型
                    
                    # 转录
                    result = model.transcribe(
                        audio_path,
                        language=language,
                        task="transcribe",
                        verbose=False,
                        fp16=False,  # 不使用半精度
                        no_speech_threshold=0.6,
                        logprob_threshold=-1.0
                    )
                    
                    return result
                except Exception as e:
                    logger.error(f"Whisper转录错误: {e}")
                    raise
            
            # 在线程池中运行
            result = await loop.run_in_executor(None, run_whisper)
            
            # 处理结果
            segments = []
            for segment in result["segments"]:
                segments.append({
                    "start": segment["start"],
                    "end": segment["end"],
                    "text": segment["text"].strip()
                })
            
            logger.info(f"语音识别完成，识别到 {len(segments)} 个片段")
            
            return {
                "language": result["language"],
                "segments": segments,
                "full_text": result["text"]
            }
            
        except ImportError:
            raise RuntimeError("Whisper未安装，请运行: pip install openai-whisper")
        except Exception as e:
            raise RuntimeError(f"语音识别失败: {str(e)}")
    
    def generate_srt_from_segments(self, segments: List[Dict]) -> str:
        """从片段生成SRT字幕"""
        srt_lines = []
        
        for i, segment in enumerate(segments, 1):
            start_time = self._seconds_to_srt_time(segment["start"])
            end_time = self._seconds_to_srt_time(segment["end"])
            text = segment["text"]
            
            srt_block = f"""{i}
{start_time} --> {end_time}
{text}"""
            srt_lines.append(srt_block)
        
        return '\n\n'.join(srt_lines)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """秒数转SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def translate_text_simple(self, text: str, target_language: str, 
                                  source_language: str = "auto") -> str:
        """简单的文本翻译 - 使用免费API或本地简单翻译"""
        try:
            # 这里可以集成免费的翻译API
            # 为了演示，我们使用一个简单的映射
            
            # 如果是中文到英文的简单映射
            if target_language == "en" and any(ord(char) > 127 for char in text):
                # 简单的中英对照（实际应用中应该使用真正的翻译API）
                simple_translations = {
                    "你好": "Hello",
                    "谢谢": "Thank you",
                    "再见": "Goodbye",
                    "是的": "Yes",
                    "不是": "No"
                }
                
                for zh, en in simple_translations.items():
                    text = text.replace(zh, en)
            
            # 如果有可用的翻译服务，在这里调用
            # 例如：Google Translate API, Microsoft Translator, 等
            
            return text
            
        except Exception as e:
            logger.warning(f"翻译失败，返回原文: {e}")
            return text
    
    async def translate_srt_content(self, srt_content: str, target_language: str,
                                  source_language: str = "auto") -> str:
        """翻译SRT内容"""
        try:
            lines = srt_content.split('\n')
            translated_lines = []
            
            for line in lines:
                line = line.strip()
                
                # 跳过序号和时间戳
                if line.isdigit() or '-->' in line or not line:
                    translated_lines.append(line)
                else:
                    # 翻译文本行
                    translated_text = await self.translate_text_simple(
                        line, target_language, source_language
                    )
                    translated_lines.append(translated_text)
            
            return '\n'.join(translated_lines)
            
        except Exception as e:
            logger.error(f"SRT翻译失败: {e}")
            return srt_content
    
    async def extract_existing_subtitles(self, video_path: str) -> Optional[str]:
        """提取视频中的现有字幕"""
        try:
            subtitle_path = os.path.join(self.temp_dir, "existing_subtitles.srt")
            
            cmd = [
                'ffmpeg', '-i', video_path,
                '-map', '0:s:0',
                '-c:s', 'srt',
                '-y', subtitle_path
            ]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await asyncio.wait_for(process.communicate(), timeout=60)
            
            if process.returncode == 0 and os.path.exists(subtitle_path):
                with open(subtitle_path, 'r', encoding='utf-8', errors='ignore') as f:
                    return f.read()
            
            return None
            
        except Exception:
            return None
    
    async def process_video_lightweight(self, video_path: str, target_language: str = "zh",
                                      source_language: str = "auto", 
                                      force_transcribe: bool = False) -> Dict[str, Any]:
        """轻量级视频处理流程"""
        try:
            video_file = Path(video_path)
            
            # 验证文件
            if not video_file.exists():
                return {"error": f"视频文件不存在: {video_path}", "success": False}
            
            if video_file.suffix.lower() not in self.supported_formats:
                return {"error": f"不支持的格式: {video_file.suffix}", "success": False}
            
            file_size = video_file.stat().st_size
            if file_size > self.max_file_size:
                return {"error": f"文件太大: {file_size/(1024*1024):.1f}MB > {self.max_file_size/(1024*1024)}MB", "success": False}
            
            # 检查依赖
            deps = self.check_dependencies()
            if not deps['ffmpeg']:
                return {"error": "FFmpeg不可用", "success": False}
            
            result = {
                "video_file": video_file.name,
                "file_size_mb": round(file_size / (1024*1024), 2),
                "target_language": target_language,
                "source_language": source_language,
                "processing_steps": [],
                "success": False
            }
            
            original_subtitles = None
            
            # 步骤1: 尝试提取现有字幕
            if not force_transcribe:
                logger.info("检查现有字幕...")
                result["processing_steps"].append("检查现有字幕")
                
                original_subtitles = await self.extract_existing_subtitles(video_path)
                
                if original_subtitles and len(original_subtitles.strip()) > 50:
                    logger.info("发现现有字幕")
                    result["processing_steps"].append("发现现有字幕")
                    result["subtitle_source"] = "existing"
            
            # 步骤2: 如果没有现有字幕，使用Whisper
            if not original_subtitles or force_transcribe:
                if not deps['whisper']:
                    return {"error": "需要Whisper进行语音识别，但Whisper不可用", "success": False}
                
                logger.info("开始语音识别流程...")
                result["processing_steps"].append("提取音频")
                
                # 提取音频
                audio_path = await self.extract_audio_from_video(video_path)
                
                # 语音识别
                result["processing_steps"].append("语音识别")
                transcription = await self.transcribe_with_whisper_safe(
                    audio_path, 
                    source_language if source_language != "auto" else None
                )
                
                # 生成字幕
                original_subtitles = self.generate_srt_from_segments(transcription["segments"])
                result["detected_language"] = transcription["language"]
                result["subtitle_source"] = "whisper"
                result["processing_steps"].append("生成字幕")
            
            if not original_subtitles:
                return {"error": "无法获取字幕内容", "success": False}
            
            result["original_subtitles"] = original_subtitles
            
            # 步骤3: 翻译字幕
            logger.info("翻译字幕...")
            result["processing_steps"].append("翻译字幕")
            
            translated_subtitles = await self.translate_srt_content(
                original_subtitles, 
                target_language,
                result.get("detected_language", source_language)
            )
            
            result["translated_subtitles"] = translated_subtitles
            
            result["processing_steps"].append("完成")
            result["success"] = True
            
            logger.info("视频处理完成")
            return result
            
        except Exception as e:
            logger.error(f"视频处理异常: {e}")
            return {
                "error": f"处理失败: {str(e)}",
                "success": False,
                "video_file": Path(video_path).name if video_path else "unknown"
            }
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass
