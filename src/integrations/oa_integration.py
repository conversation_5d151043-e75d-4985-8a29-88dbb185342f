"""
支持 Odoo、钉钉、企业微信等 OA 系统
"""

import httpx
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

class OAIntegration:
    """OA 系统集成类"""

    def __init__(self, qwen_api_url: str = "http://localhost:8000"):
        self.qwen_api_url = qwen_api_url
        self.client = httpx.AsyncClient(timeout=60.0)

    async def analyze_approval_document(
        self,
        document_content: str,
        approval_type: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """分析审批文档"""

        system_prompt = f"""
        你是一个专业的 OA 审批助手。请分析以下{approval_type}审批文档，并提供：
        1. 审批建议（通过/拒绝/需要补充材料）
        2. 风险评估（1-10分，10分为最高风险）
        3. 具体原因和建议
        4. 需要关注的要点

        请以结构化格式返回结果。
        """

        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": document_content,
                    "system_prompt": system_prompt,
                    "temperature": 0.3,
                    "max_tokens": 1024
                }
            )

            result = response.json()

            # 解析 AI 回复
            ai_response = result["response"]

            return {
                "approval_type": approval_type,
                "analysis": ai_response,
                "timestamp": datetime.now().isoformat(),
                "processing_time": result["processing_time"],
                "context": context
            }

        except Exception as e:
            return {
                "error": str(e),
                "approval_type": approval_type,
                "timestamp": datetime.now().isoformat()
            }
    
    async def generate_approval_summary(
        self, 
        approvals: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成审批汇总报告"""
        
        summary_text = "\n".join([
            f"- {approval.get('type', '未知类型')}: {approval.get('title', '无标题')}"
            for approval in approvals
        ])
        
        system_prompt = """
        请分析以下审批列表，生成一份管理汇总报告，包括：
        1. 审批数量统计
        2. 主要审批类型分布
        3. 需要重点关注的审批
        4. 管理建议
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/summarize",
                json={
                    "text": summary_text,
                    "max_length": 300,
                    "language": "zh"
                }
            )
            
            result = response.json()
            
            return {
                "summary": result["summary"],
                "total_approvals": len(approvals),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# Odoo 集成示例
class OdooIntegration(OAIntegration):
    """Odoo ERP 系统集成"""
    
    def __init__(self, odoo_url: str, database: str, username: str, password: str):
        super().__init__()
        self.odoo_url = odoo_url
        self.database = database
        self.username = username
        self.password = password
        self.uid = None
    
    async def authenticate(self):
        """Odoo 认证"""
        # 这里实现 Odoo XML-RPC 认证逻辑
        pass
    
    async def process_purchase_request(self, request_id: int):
        """处理采购申请"""
        # 1. 从 Odoo 获取采购申请详情
        # 2. 调用 AI 分析
        # 3. 更新 Odoo 记录
        pass

# 钉钉集成示例
class DingTalkIntegration(OAIntegration):
    """钉钉 OA 集成"""
    
    def __init__(self, app_key: str, app_secret: str):
        super().__init__()
        self.app_key = app_key
        self.app_secret = app_secret
        self.access_token = None
    
    async def get_access_token(self):
        """获取钉钉访问令牌"""
        url = "https://oapi.dingtalk.com/gettoken"
        params = {
            "appkey": self.app_key,
            "appsecret": self.app_secret
        }
        
        response = await self.client.get(url, params=params)
        result = response.json()
        
        if result.get("errcode") == 0:
            self.access_token = result["access_token"]
            return True
        return False
    
    async def process_approval_instance(self, process_instance_id: str):
        """处理钉钉审批实例"""
        if not self.access_token:
            await self.get_access_token()
        
        # 获取审批详情
        url = "https://oapi.dingtalk.com/topapi/processinstance/get"
        data = {
            "access_token": self.access_token,
            "process_instance_id": process_instance_id
        }
        
        response = await self.client.post(url, json=data)
        approval_data = response.json()
        
        if approval_data.get("errcode") == 0:
            # 调用 AI 分析
            analysis = await self.analyze_approval_document(
                document_content=str(approval_data["result"]),
                approval_type="钉钉审批"
            )
            
            return analysis
        
        return {"error": "获取审批详情失败"}

# 使用示例
async def example_usage():
    """使用示例"""
    
    # 1. 基础 OA 集成
    oa = OAIntegration()
    
    # 分析请假申请
    leave_request = """
    申请人：张三
    部门：技术部
    请假类型：年假
    请假时间：2024-01-15 至 2024-01-20
    请假天数：5天
    请假原因：家庭旅行
    """
    
    result = await oa.analyze_approval_document(
        document_content=leave_request,
        approval_type="请假申请"
    )
    
    print("审批分析结果:", result)
    
    # 2. 钉钉集成
    dingtalk = DingTalkIntegration(
        app_key="your_app_key",
        app_secret="your_app_secret"
    )
    
    # 处理钉钉审批
    dingtalk_result = await dingtalk.process_approval_instance("process_id_123")
    print("钉钉审批结果:", dingtalk_result)

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
