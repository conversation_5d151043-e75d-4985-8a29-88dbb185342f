"""
稳定的视频处理器 - 避免崩溃
"""

import os
import subprocess
import tempfile
import asyncio
import time
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import shutil

logger = logging.getLogger(__name__)

class StableVideoProcessor:
    """稳定的视频处理器"""
    
    def __init__(self):
        self.temp_dir = None
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv']
        self._create_temp_dir()
        
    def _create_temp_dir(self):
        """安全地创建临时目录"""
        try:
            self.temp_dir = tempfile.mkdtemp(prefix="stable_video_")
            logger.info(f"创建临时目录: {self.temp_dir}")
        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            self.temp_dir = "/tmp/stable_video_fallback"
            os.makedirs(self.temp_dir, exist_ok=True)
    
    def check_dependencies(self) -> Dict[str, bool]:
        """安全地检查依赖"""
        dependencies = {}
        
        # 检查 ffmpeg
        try:
            result = subprocess.run(
                ['ffmpeg', '-version'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            dependencies['ffmpeg'] = result.returncode == 0
            logger.info(f"FFmpeg 检查: {'可用' if dependencies['ffmpeg'] else '不可用'}")
        except Exception as e:
            dependencies['ffmpeg'] = False
            logger.warning(f"FFmpeg 检查失败: {e}")
        
        # 检查 whisper (安全检查)
        dependencies['whisper'] = False
        try:
            # 不直接导入，避免崩溃
            result = subprocess.run(
                ['python', '-c', 'import whisper; print("OK")'],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0 and "OK" in result.stdout:
                dependencies['whisper'] = True
                logger.info("Whisper 可用")
            else:
                logger.info("Whisper 不可用")
        except Exception as e:
            logger.warning(f"Whisper 检查失败: {e}")
        
        return dependencies
    
    async def extract_existing_subtitles(self, video_path: str) -> Optional[str]:
        """安全地提取现有字幕"""
        if not self.temp_dir:
            return None
            
        try:
            subtitle_path = os.path.join(self.temp_dir, "extracted_subtitles.srt")
            
            cmd = [
                'ffmpeg', '-i', video_path,
                '-map', '0:s:0',  # 选择第一个字幕流
                '-c:s', 'srt',    # 转换为SRT格式
                '-y',             # 覆盖输出文件
                subtitle_path
            ]
            
            logger.info(f"提取字幕命令: {' '.join(cmd)}")
            
            # 使用更安全的进程创建方式
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=30
                )
            except asyncio.TimeoutError:
                logger.warning("字幕提取超时")
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass
                return None
            
            if process.returncode == 0 and os.path.exists(subtitle_path):
                with open(subtitle_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    logger.info(f"成功提取字幕，长度: {len(content)}")
                    return content
            else:
                stderr_text = stderr.decode('utf-8', errors='ignore') if stderr else ""
                logger.info(f"未找到字幕或提取失败: {stderr_text}")
                return None
                
        except Exception as e:
            logger.error(f"字幕提取异常: {e}")
            return None
    
    async def process_video_safe(self, video_path: str, target_language: str = "zh") -> Dict[str, Any]:
        """安全的视频处理流程"""
        try:
            video_file = Path(video_path)
            
            if not video_file.exists():
                return {
                    "error": f"视频文件不存在: {video_path}",
                    "success": False
                }
            
            if video_file.suffix.lower() not in self.supported_formats:
                return {
                    "error": f"不支持的视频格式: {video_file.suffix}",
                    "success": False
                }
            
            # 检查依赖
            deps = self.check_dependencies()
            if not deps['ffmpeg']:
                return {
                    "error": "FFmpeg 不可用",
                    "success": False
                }
            
            result = {
                "video_file": video_file.name,
                "target_language": target_language,
                "processing_steps": [],
                "success": False
            }
            
            # 尝试提取现有字幕
            logger.info("检查视频中的现有字幕...")
            result["processing_steps"].append("检查现有字幕")
            
            existing_subtitles = await self.extract_existing_subtitles(video_path)
            
            if existing_subtitles and len(existing_subtitles.strip()) > 0:
                logger.info("发现现有字幕，开始翻译...")
                result["processing_steps"].append("发现现有字幕")
                result["original_subtitles"] = existing_subtitles
                
                # 翻译字幕
                try:
                    from .subtitle_translation import SubtitleTranslator
                    
                    translator = SubtitleTranslator()
                    translation_result = await translator.translate_srt_file(
                        existing_subtitles, target_language
                    )
                    await translator.close()
                    
                    if "error" not in translation_result:
                        result["translated_subtitles"] = translation_result["translated_srt"]
                        result["processing_steps"].append("翻译完成")
                        result["success"] = True
                        logger.info("字幕翻译成功")
                    else:
                        result["error"] = f"翻译失败: {translation_result['error']}"
                        
                except Exception as e:
                    result["error"] = f"翻译过程出错: {str(e)}"
                    logger.error(f"翻译异常: {e}")
            
            else:
                # 没有现有字幕
                result["error"] = "视频中未找到字幕，且语音识别功能不可用"
                result["suggestion"] = "请上传包含字幕的视频文件，或使用字幕翻译功能"
                logger.info("未找到现有字幕")
            
            return result
            
        except Exception as e:
            logger.error(f"视频处理异常: {e}")
            return {
                "error": f"视频处理失败: {str(e)}",
                "success": False,
                "video_file": Path(video_path).name if video_path else "unknown"
            }
    
    def cleanup(self):
        """安全地清理临时文件"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
                logger.info(f"清理临时目录: {self.temp_dir}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass

class SimpleVideoUploadHandler:
    """简单的视频上传处理器"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        self.max_file_size = 100 * 1024 * 1024  # 100MB (降低限制)
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """安全地保存上传文件"""
        try:
            # 检查文件大小
            if len(file_content) > self.max_file_size:
                raise ValueError(f"文件太大，最大支持 {self.max_file_size // (1024*1024)}MB")
            
            # 生成安全的文件名
            safe_filename = self._sanitize_filename(filename)
            file_path = self.upload_dir / safe_filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"文件已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            raise RuntimeError(f"文件保存失败: {str(e)}")
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名"""
        import re
        
        # 保留扩展名
        name, ext = os.path.splitext(filename)
        
        # 清理文件名
        safe_name = re.sub(r'[^\w\-_.]', '_', name)
        
        # 添加时间戳避免冲突
        timestamp = str(int(time.time()))
        
        return f"{safe_name}_{timestamp}{ext}"
    
    def cleanup_old_files(self, max_age_hours: int = 24):
        """清理旧文件"""
        try:
            current_time = time.time()
            
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_hours * 3600:
                        file_path.unlink()
                        logger.info(f"删除旧文件: {file_path.name}")
                        
        except Exception as e:
            logger.warning(f"清理旧文件失败: {e}")

# 为了兼容性，提供别名
VideoProcessor = StableVideoProcessor
VideoUploadHandler = SimpleVideoUploadHandler
