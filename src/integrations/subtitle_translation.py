"""
视频字幕翻译集成
"""

import re
import asyncio
import httpx
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

class SubtitleTranslator:
    """字幕翻译器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def translate_srt_file(self, srt_content: str, target_language: str = "zh", 
                               source_language: str = "auto") -> Dict[str, Any]:
        """翻译SRT字幕文件"""
        try:
            # 解析SRT文件
            subtitles = self._parse_srt(srt_content)
            
            if not subtitles:
                return {"error": "无法解析字幕文件"}
            
            print(f"📝 解析到 {len(subtitles)} 条字幕")
            
            # 批量翻译
            translated_subtitles = await self._translate_subtitles_batch(
                subtitles, target_language, source_language
            )
            
            # 生成翻译后的SRT内容
            translated_srt = self._generate_srt(translated_subtitles)
            
            return {
                "original_count": len(subtitles),
                "translated_count": len(translated_subtitles),
                "source_language": source_language,
                "target_language": target_language,
                "translated_srt": translated_srt,
                "subtitles": translated_subtitles,
                "translated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": f"字幕翻译失败: {str(e)}"}
    
    def _parse_srt(self, srt_content: str) -> List[Dict[str, Any]]:
        """解析SRT字幕格式"""
        subtitles = []
        
        # 分割字幕块
        blocks = re.split(r'\n\s*\n', srt_content.strip())
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                try:
                    # 序号
                    index = int(lines[0])
                    
                    # 时间轴
                    time_line = lines[1]
                    time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', time_line)
                    
                    if time_match:
                        start_time = time_match.group(1)
                        end_time = time_match.group(2)
                        
                        # 字幕文本（可能多行）
                        text = '\n'.join(lines[2:])
                        
                        subtitles.append({
                            "index": index,
                            "start_time": start_time,
                            "end_time": end_time,
                            "original_text": text,
                            "translated_text": ""
                        })
                        
                except (ValueError, IndexError):
                    continue
        
        return subtitles
    
    async def _translate_subtitles_batch(self, subtitles: List[Dict[str, Any]], 
                                       target_language: str, source_language: str) -> List[Dict[str, Any]]:
        """批量翻译字幕"""
        translated_subtitles = []
        batch_size = 5  # 每批处理5条字幕
        
        for i in range(0, len(subtitles), batch_size):
            batch = subtitles[i:i + batch_size]
            
            print(f"🔄 翻译第 {i//batch_size + 1} 批 ({len(batch)} 条字幕)")
            
            # 并发翻译这一批字幕
            tasks = []
            for subtitle in batch:
                task = self._translate_single_subtitle(
                    subtitle, target_language, source_language
                )
                tasks.append(task)
            
            # 等待这一批完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    print(f"⚠️  字幕 {batch[j]['index']} 翻译失败: {result}")
                    # 保留原文
                    batch[j]["translated_text"] = batch[j]["original_text"]
                    translated_subtitles.append(batch[j])
                else:
                    translated_subtitles.append(result)
            
            # 避免请求过于频繁
            await asyncio.sleep(1)
        
        return translated_subtitles
    
    async def _translate_single_subtitle(self, subtitle: Dict[str, Any], 
                                       target_language: str, source_language: str) -> Dict[str, Any]:
        """翻译单条字幕"""
        original_text = subtitle["original_text"].strip()
        
        if not original_text:
            subtitle["translated_text"] = ""
            return subtitle
        
        # 构建翻译提示
        language_map = {
            "zh": "中文",
            "en": "英文", 
            "ja": "日文",
            "ko": "韩文",
            "fr": "法文",
            "de": "德文",
            "es": "西班牙文",
            "ru": "俄文"
        }
        
        target_lang_name = language_map.get(target_language, target_language)
        
        system_prompt = f"""你是专业的字幕翻译员。请将以下字幕翻译成{target_lang_name}。

翻译要求：
1. 保持字幕的简洁性，适合观看
2. 保留原意，但要符合目标语言习惯
3. 如果是对话，保持口语化
4. 如果是旁白，保持正式一些
5. 只返回翻译结果，不要其他内容"""
        
        try:
            response = await self.client.post(
                f"{self.api_base_url}/chat",
                json={
                    "message": original_text,
                    "system_prompt": system_prompt,
                    "max_tokens": 200,
                    "temperature": 0.3
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                translated_text = data.get("response", "").strip()
                
                # 清理翻译结果
                translated_text = self._clean_translation(translated_text)
                
                subtitle["translated_text"] = translated_text
                return subtitle
            else:
                print(f"翻译请求失败: {response.status_code}")
                subtitle["translated_text"] = original_text
                return subtitle
                
        except Exception as e:
            print(f"翻译异常: {e}")
            subtitle["translated_text"] = original_text
            return subtitle
    
    def _clean_translation(self, text: str) -> str:
        """清理翻译结果"""
        # 移除可能的引号
        text = text.strip('"\'""''')
        
        # 移除多余的换行
        text = re.sub(r'\n+', '\n', text)
        
        # 移除开头的"翻译："等前缀
        text = re.sub(r'^(翻译[：:]|译文[：:]|结果[：:])', '', text)
        
        return text.strip()
    
    def _generate_srt(self, subtitles: List[Dict[str, Any]]) -> str:
        """生成SRT格式字幕"""
        srt_content = []
        
        for subtitle in subtitles:
            srt_block = f"""{subtitle['index']}
{subtitle['start_time']} --> {subtitle['end_time']}
{subtitle['translated_text']}"""
            srt_content.append(srt_block)
        
        return '\n\n'.join(srt_content)
    
    async def translate_vtt_file(self, vtt_content: str, target_language: str = "zh") -> Dict[str, Any]:
        """翻译VTT字幕文件"""
        try:
            # 简单的VTT解析（基础实现）
            lines = vtt_content.split('\n')
            subtitles = []
            current_subtitle = {}
            
            for line in lines:
                line = line.strip()
                
                # 跳过VTT头部
                if line.startswith('WEBVTT') or line.startswith('NOTE'):
                    continue
                
                # 时间轴行
                if '-->' in line:
                    time_match = re.match(r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})', line)
                    if time_match:
                        current_subtitle = {
                            "start_time": time_match.group(1),
                            "end_time": time_match.group(2),
                            "original_text": "",
                            "translated_text": ""
                        }
                
                # 字幕文本
                elif line and current_subtitle:
                    current_subtitle["original_text"] = line
                    subtitles.append(current_subtitle)
                    current_subtitle = {}
            
            # 翻译字幕
            translated_subtitles = await self._translate_subtitles_batch(
                subtitles, target_language, "auto"
            )
            
            # 生成VTT内容
            translated_vtt = self._generate_vtt(translated_subtitles)
            
            return {
                "original_count": len(subtitles),
                "translated_count": len(translated_subtitles),
                "target_language": target_language,
                "translated_vtt": translated_vtt,
                "subtitles": translated_subtitles
            }
            
        except Exception as e:
            return {"error": f"VTT翻译失败: {str(e)}"}
    
    def _generate_vtt(self, subtitles: List[Dict[str, Any]]) -> str:
        """生成VTT格式字幕"""
        vtt_content = ["WEBVTT", ""]
        
        for subtitle in subtitles:
            vtt_block = f"""{subtitle['start_time']} --> {subtitle['end_time']}
{subtitle['translated_text']}"""
            vtt_content.append(vtt_block)
            vtt_content.append("")
        
        return '\n'.join(vtt_content)
    
    async def create_bilingual_subtitles(self, srt_content: str, target_language: str = "zh") -> Dict[str, Any]:
        """创建双语字幕"""
        try:
            # 解析原字幕
            subtitles = self._parse_srt(srt_content)
            
            # 翻译字幕
            translated_subtitles = await self._translate_subtitles_batch(
                subtitles, target_language, "auto"
            )
            
            # 生成双语字幕
            bilingual_srt = self._generate_bilingual_srt(translated_subtitles)
            
            return {
                "subtitle_count": len(translated_subtitles),
                "target_language": target_language,
                "bilingual_srt": bilingual_srt,
                "subtitles": translated_subtitles
            }
            
        except Exception as e:
            return {"error": f"双语字幕创建失败: {str(e)}"}
    
    def _generate_bilingual_srt(self, subtitles: List[Dict[str, Any]]) -> str:
        """生成双语SRT字幕"""
        srt_content = []
        
        for subtitle in subtitles:
            # 双语显示：原文在上，译文在下
            bilingual_text = f"{subtitle['original_text']}\n{subtitle['translated_text']}"
            
            srt_block = f"""{subtitle['index']}
{subtitle['start_time']} --> {subtitle['end_time']}
{bilingual_text}"""
            srt_content.append(srt_block)
        
        return '\n\n'.join(srt_content)
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
