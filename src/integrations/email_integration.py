"""
邮件系统集成示例
支持 Mailcow、Postfix、Exchange 等邮件系统
"""

import httpx
import json
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, Any, List, Optional
from datetime import datetime
import smtplib
import imaplib

class EmailIntegration:
    """邮件系统集成类"""
    
    def __init__(self, qwen_api_url: str = "http://localhost:8000"):
        self.qwen_api_url = qwen_api_url
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def classify_email(
        self,
        subject: str,
        content: str,
        sender: Optional[str] = None
    ) -> Dict[str, Any]:
        """邮件分类和分析"""
        
        system_prompt = """
        请分析以下邮件，并提供：
        1. 邮件类别（工作/个人/营销/垃圾邮件/紧急/通知等）
        2. 优先级（1-5，5为最高）
        3. 情感倾向（积极/中性/消极）
        4. 是否需要回复
        5. 建议的处理方式
        6. 关键信息提取
        
        请以结构化格式返回。
        """
        
        email_text = f"主题：{subject}\n发件人：{sender}\n内容：{content}"
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": email_text,
                    "system_prompt": system_prompt,
                    "temperature": 0.3,
                    "max_tokens": 800
                }
            )
            
            result = response.json()
            
            return {
                "subject": subject,
                "sender": sender,
                "classification": result["response"],
                "analyzed_at": datetime.now().isoformat(),
                "processing_time": result["processing_time"]
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "subject": subject,
                "analyzed_at": datetime.now().isoformat()
            }
    
    async def generate_email_reply(
        self,
        original_email: str,
        reply_type: str = "professional",
        key_points: Optional[List[str]] = None,
        tone: str = "formal"
    ) -> Dict[str, Any]:
        """生成邮件回复"""
        
        points_text = ""
        if key_points:
            points_text = f"需要包含的要点：{', '.join(key_points)}"
        
        system_prompt = f"""
        请为以下邮件生成一个{tone}的{reply_type}回复：
        
        原邮件：{original_email}
        {points_text}
        
        回复要求：
        1. 语调{tone}且礼貌
        2. 内容简洁明了
        3. 回应原邮件的关键问题
        4. 包含适当的开头和结尾
        5. 符合商务邮件规范
        
        请直接生成回复内容。
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/chat",
                json={
                    "message": original_email,
                    "system_prompt": system_prompt,
                    "temperature": 0.4,
                    "max_tokens": 1000
                }
            )
            
            result = response.json()
            
            return {
                "reply_content": result["response"],
                "reply_type": reply_type,
                "tone": tone,
                "key_points": key_points,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    async def extract_email_insights(
        self,
        emails: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """提取邮件洞察"""
        
        # 合并邮件内容进行分析
        email_summary = "\n".join([
            f"主题：{email.get('subject', '')}\n发件人：{email.get('sender', '')}\n"
            for email in emails[:20]  # 限制分析数量
        ])
        
        system_prompt = """
        请分析以下邮件列表，提供：
        1. 邮件数量统计
        2. 主要发件人分析
        3. 邮件主题趋势
        4. 紧急邮件识别
        5. 工作效率建议
        6. 需要关注的邮件
        
        请提供管理建议。
        """
        
        try:
            response = await self.client.post(
                f"{self.qwen_api_url}/summarize",
                json={
                    "text": email_summary,
                    "max_length": 400,
                    "language": "zh"
                }
            )
            
            result = response.json()
            
            return {
                "total_emails": len(emails),
                "insights": result["summary"],
                "analyzed_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# Mailcow 集成示例
class MailcowIntegration(EmailIntegration):
    """Mailcow 邮件系统集成"""
    
    def __init__(self, mailcow_url: str, api_key: str):
        super().__init__()
        self.mailcow_url = mailcow_url
        self.api_key = api_key
    
    async def get_mailbox_emails(
        self,
        mailbox: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取邮箱邮件"""
        
        headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json"
        }
        
        url = f"{self.mailcow_url}/api/v1/get/mailbox/{mailbox}"
        
        try:
            response = await self.client.get(url, headers=headers)
            data = response.json()
            
            # 这里需要根据 Mailcow API 的实际响应格式调整
            emails = []
            for email_data in data.get("emails", [])[:limit]:
                emails.append({
                    "id": email_data.get("id"),
                    "subject": email_data.get("subject"),
                    "sender": email_data.get("from"),
                    "content": email_data.get("body"),
                    "received_at": email_data.get("date")
                })
            
            return emails
            
        except Exception as e:
            return [{"error": str(e)}]
    
    async def process_mailbox_with_ai(self, mailbox: str) -> Dict[str, Any]:
        """使用 AI 处理邮箱"""
        
        # 1. 获取邮件
        emails = await self.get_mailbox_emails(mailbox)
        
        if not emails or emails[0].get("error"):
            return {"error": "无法获取邮件"}
        
        # 2. 分类每封邮件
        classified_emails = []
        for email_data in emails:
            if email_data.get("subject") and email_data.get("content"):
                classification = await self.classify_email(
                    subject=email_data["subject"],
                    content=email_data["content"],
                    sender=email_data.get("sender")
                )
                
                classified_emails.append({
                    "email_id": email_data["id"],
                    "classification": classification
                })
        
        # 3. 生成邮箱洞察
        insights = await self.extract_email_insights(emails)
        
        return {
            "mailbox": mailbox,
            "total_emails": len(emails),
            "classified_emails": classified_emails,
            "insights": insights,
            "processed_at": datetime.now().isoformat()
        }

# IMAP/SMTP 通用集成
class IMAPSMTPIntegration(EmailIntegration):
    """IMAP/SMTP 通用邮件集成"""
    
    def __init__(
        self,
        imap_server: str,
        smtp_server: str,
        username: str,
        password: str,
        imap_port: int = 993,
        smtp_port: int = 587
    ):
        super().__init__()
        self.imap_server = imap_server
        self.smtp_server = smtp_server
        self.username = username
        self.password = password
        self.imap_port = imap_port
        self.smtp_port = smtp_port
    
    def connect_imap(self):
        """连接 IMAP 服务器"""
        try:
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.username, self.password)
            return mail
        except Exception as e:
            print(f"IMAP 连接失败: {e}")
            return None
    
    def connect_smtp(self):
        """连接 SMTP 服务器"""
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            return server
        except Exception as e:
            print(f"SMTP 连接失败: {e}")
            return None
    
    async def fetch_recent_emails(self, folder: str = "INBOX", count: int = 10):
        """获取最近邮件"""
        
        mail = self.connect_imap()
        if not mail:
            return []
        
        try:
            mail.select(folder)
            
            # 搜索最近的邮件
            status, messages = mail.search(None, "ALL")
            email_ids = messages[0].split()[-count:]  # 获取最近的邮件
            
            emails = []
            for email_id in email_ids:
                status, msg_data = mail.fetch(email_id, "(RFC822)")
                
                for response_part in msg_data:
                    if isinstance(response_part, tuple):
                        msg = email.message_from_bytes(response_part[1])
                        
                        # 提取邮件信息
                        subject = msg["subject"]
                        sender = msg["from"]
                        
                        # 获取邮件内容
                        content = ""
                        if msg.is_multipart():
                            for part in msg.walk():
                                if part.get_content_type() == "text/plain":
                                    content = part.get_payload(decode=True).decode()
                                    break
                        else:
                            content = msg.get_payload(decode=True).decode()
                        
                        emails.append({
                            "id": email_id.decode(),
                            "subject": subject,
                            "sender": sender,
                            "content": content[:1000]  # 限制内容长度
                        })
            
            mail.close()
            mail.logout()
            
            return emails
            
        except Exception as e:
            print(f"获取邮件失败: {e}")
            return []
    
    async def send_ai_reply(
        self,
        to_email: str,
        subject: str,
        original_content: str,
        reply_type: str = "professional"
    ):
        """发送 AI 生成的回复"""
        
        # 1. 生成回复内容
        reply_data = await self.generate_email_reply(
            original_email=original_content,
            reply_type=reply_type
        )
        
        if reply_data.get("error"):
            return {"error": reply_data["error"]}
        
        # 2. 发送邮件
        smtp = self.connect_smtp()
        if not smtp:
            return {"error": "SMTP 连接失败"}
        
        try:
            msg = MIMEMultipart()
            msg["From"] = self.username
            msg["To"] = to_email
            msg["Subject"] = f"Re: {subject}"
            
            msg.attach(MIMEText(reply_data["reply_content"], "plain", "utf-8"))
            
            smtp.send_message(msg)
            smtp.quit()
            
            return {
                "success": True,
                "reply_sent": True,
                "reply_content": reply_data["reply_content"]
            }
            
        except Exception as e:
            return {"error": f"发送邮件失败: {e}"}

# 使用示例
async def example_usage():
    """使用示例"""
    
    # 1. 基础邮件分析
    email_ai = EmailIntegration()
    
    classification = await email_ai.classify_email(
        subject="紧急：项目进度汇报",
        content="请尽快提供项目最新进度，明天需要向客户汇报。",
        sender="<EMAIL>"
    )
    
    print("邮件分类:", classification)
    
    # 2. 生成回复
    reply = await email_ai.generate_email_reply(
        original_email="您好，请问下周的会议安排是什么时候？",
        reply_type="professional",
        key_points=["确认时间", "提供会议室信息"],
        tone="formal"
    )
    
    print("生成回复:", reply)
    
    # 3. IMAP/SMTP 集成示例
    imap_smtp = IMAPSMTPIntegration(
        imap_server="imap.gmail.com",
        smtp_server="smtp.gmail.com",
        username="<EMAIL>",
        password="your_password"
    )
    
    # 获取最近邮件
    recent_emails = await imap_smtp.fetch_recent_emails(count=5)
    print(f"获取到 {len(recent_emails)} 封邮件")

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
