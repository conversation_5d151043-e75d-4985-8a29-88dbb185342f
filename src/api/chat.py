"""
聊天相关 API 路由
"""

import httpx
import uuid
import json
from datetime import datetime
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

router = APIRouter(prefix="/chat", tags=["chat"])

# 全局 HTTP 客户端
http_client = httpx.AsyncClient(timeout=60.0)

# 内存中的对话存储（生产环境应使用数据库）
conversations_store: Dict[str, List[Dict[str, Any]]] = {}

class ChatMessage(BaseModel):
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: Optional[str] = None

class ConversationRequest(BaseModel):
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1024

class ConversationResponse(BaseModel):
    message: ChatMessage
    conversation_id: str
    tokens_used: int
    processing_time: float

async def call_qwen_api(messages: List[ChatMessage], temperature: float = 0.7, max_tokens: int = 1024) -> Dict[str, Any]:
    """调用 Qwen API 进行对话"""
    try:
        # 构建对话历史
        conversation_text = ""
        system_prompt = ""

        for msg in messages:
            if msg.role == "system":
                system_prompt = msg.content
            elif msg.role == "user":
                conversation_text += f"用户: {msg.content}\n"
            elif msg.role == "assistant":
                conversation_text += f"助手: {msg.content}\n"

        # 获取最后一个用户消息作为当前输入
        last_user_message = ""
        for msg in reversed(messages):
            if msg.role == "user":
                last_user_message = msg.content
                break

        # 如果有对话历史，将其作为上下文
        if len([m for m in messages if m.role in ["user", "assistant"]]) > 1:
            context_messages = [m for m in messages[:-1] if m.role in ["user", "assistant"]]
            context = "\n".join([f"{m.role}: {m.content}" for m in context_messages[-6:]])  # 最近6轮对话
            full_prompt = f"对话历史:\n{context}\n\n当前问题: {last_user_message}"
        else:
            full_prompt = last_user_message

        response = await http_client.post(
            "http://localhost:8000/chat",
            json={
                "message": full_prompt,
                "system_prompt": system_prompt if system_prompt else None,
                "temperature": temperature,
                "max_tokens": max_tokens
            }
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(status_code=500, detail=f"Qwen API 调用失败: {response.status_code}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对话API调用错误: {str(e)}")

@router.post("/conversation", response_model=ConversationResponse)
async def conversation(request: ConversationRequest):
    """多轮对话接口"""

    try:
        # 生成对话ID
        conversation_id = str(uuid.uuid4())

        # 调用 Qwen API
        result = await call_qwen_api(
            messages=request.messages,
            temperature=request.temperature or 0.7,
            max_tokens=request.max_tokens or 1024
        )

        # 创建助手回复消息
        assistant_message = ChatMessage(
            role="assistant",
            content=result["response"],
            timestamp=datetime.now().isoformat()
        )

        # 保存对话历史
        conversation_history = []
        for msg in request.messages:
            conversation_history.append({
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp or datetime.now().isoformat()
            })

        conversation_history.append({
            "role": "assistant",
            "content": result["response"],
            "timestamp": datetime.now().isoformat()
        })

        conversations_store[conversation_id] = conversation_history

        return ConversationResponse(
            message=assistant_message,
            conversation_id=conversation_id,
            tokens_used=result.get("tokens_used", 0),
            processing_time=result.get("processing_time", 0.0)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")

@router.get("/conversations")
async def get_conversations(limit: int = 10, offset: int = 0):
    """获取对话历史"""
    try:
        # 获取所有对话ID和基本信息
        conversations = []

        for conv_id, messages in list(conversations_store.items())[offset:offset+limit]:
            if messages:
                # 获取第一条用户消息作为标题
                title = "新对话"
                for msg in messages:
                    if msg["role"] == "user":
                        title = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
                        break

                # 获取最后一条消息的时间
                last_message_time = messages[-1].get("timestamp", datetime.now().isoformat())

                conversations.append({
                    "conversation_id": conv_id,
                    "title": title,
                    "message_count": len(messages),
                    "last_message_time": last_message_time,
                    "preview": messages[-1]["content"][:100] + "..." if len(messages[-1]["content"]) > 100 else messages[-1]["content"]
                })

        return {
            "conversations": conversations,
            "total": len(conversations_store),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")

@router.get("/conversations/{conversation_id}")
async def get_conversation_detail(conversation_id: str):
    """获取特定对话的详细内容"""
    try:
        if conversation_id not in conversations_store:
            raise HTTPException(status_code=404, detail="对话不存在")

        messages = conversations_store[conversation_id]

        return {
            "conversation_id": conversation_id,
            "messages": messages,
            "message_count": len(messages),
            "created_at": messages[0].get("timestamp") if messages else None,
            "updated_at": messages[-1].get("timestamp") if messages else None
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取对话详情失败: {str(e)}")

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """删除对话"""
    try:
        if conversation_id not in conversations_store:
            raise HTTPException(status_code=404, detail="对话不存在")

        del conversations_store[conversation_id]

        return {
            "message": "对话删除成功",
            "conversation_id": conversation_id,
            "deleted_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除对话失败: {str(e)}")

@router.post("/conversations/{conversation_id}/continue")
async def continue_conversation(
    conversation_id: str,
    message: str,
    temperature: Optional[float] = 0.7,
    max_tokens: Optional[int] = 1024
):
    """继续特定对话"""
    try:
        if conversation_id not in conversations_store:
            raise HTTPException(status_code=404, detail="对话不存在")

        # 获取对话历史
        conversation_history = conversations_store[conversation_id]

        # 添加新的用户消息
        new_user_message = ChatMessage(
            role="user",
            content=message,
            timestamp=datetime.now().isoformat()
        )

        # 构建完整的消息列表
        all_messages = []
        for msg_dict in conversation_history:
            all_messages.append(ChatMessage(
                role=msg_dict["role"],
                content=msg_dict["content"],
                timestamp=msg_dict.get("timestamp")
            ))
        all_messages.append(new_user_message)

        # 调用 Qwen API
        result = await call_qwen_api(
            messages=all_messages,
            temperature=temperature or 0.7,
            max_tokens=max_tokens or 1024
        )

        # 创建助手回复
        assistant_message = ChatMessage(
            role="assistant",
            content=result["response"],
            timestamp=datetime.now().isoformat()
        )

        # 更新对话历史
        conversation_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })

        conversation_history.append({
            "role": "assistant",
            "content": result["response"],
            "timestamp": datetime.now().isoformat()
        })

        conversations_store[conversation_id] = conversation_history

        return ConversationResponse(
            message=assistant_message,
            conversation_id=conversation_id,
            tokens_used=result.get("tokens_used", 0),
            processing_time=result.get("processing_time", 0.0)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"继续对话失败: {str(e)}")


chat_router = router
