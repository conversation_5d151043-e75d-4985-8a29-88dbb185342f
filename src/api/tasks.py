"""
任务处理相关 API 路由
支持 OA、会议、教育、邮件等系统的 AI 任务
"""

import httpx
import json
import re
from datetime import datetime
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict, Any

router = APIRouter(prefix="/tasks", tags=["tasks"])

# 全局 HTTP 客户端
http_client = httpx.AsyncClient(timeout=60.0)

async def call_qwen_api(prompt: str, system_prompt: str, max_tokens: int = 1024) -> Dict[str, Any]:
    """调用 Qwen API 的通用函数"""
    try:
        response = await http_client.post(
            "http://localhost:8000/chat",
            json={
                "message": prompt,
                "system_prompt": system_prompt,
                "temperature": 0.3,
                "max_tokens": max_tokens
            }
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(status_code=500, detail=f"Qwen API 调用失败: {response.status_code}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"API 调用错误: {str(e)}")

# OA 系统相关
class ApprovalRequest(BaseModel):
    document_content: str
    approval_type: str  # "expense", "leave", "purchase", etc.
    context: Optional[Dict[str, Any]] = None

class ApprovalResponse(BaseModel):
    recommendation: str  # "approve", "reject", "review"
    reason: str
    risk_score: float
    suggestions: List[str]

@router.post("/oa/approval", response_model=ApprovalResponse)
async def analyze_approval(request: ApprovalRequest):
    """OA 审批分析"""

    system_prompt = f"""
    你是一个专业的 OA 审批助手。请分析以下{request.approval_type}审批文档，并提供：
    1. 审批建议（approve/reject/review）
    2. 风险评估（1-10分，10分为最高风险）
    3. 具体原因和建议
    4. 需要关注的要点

    请以 JSON 格式返回结果，包含：
    - recommendation: "approve" | "reject" | "review"
    - reason: "详细原因"
    - risk_score: 数字(1-10)
    - suggestions: ["建议1", "建议2", ...]
    """

    try:
        result = await call_qwen_api(
            prompt=request.document_content,
            system_prompt=system_prompt,
            max_tokens=1024
        )

        # 解析 AI 回复
        ai_response = result["response"]

        # 尝试从回复中提取结构化信息
        recommendation = "review"  # 默认值
        reason = ai_response
        risk_score = 5.0
        suggestions = []

        # 简单的关键词匹配来确定建议
        if "通过" in ai_response or "approve" in ai_response.lower() or "同意" in ai_response:
            recommendation = "approve"
            risk_score = 3.0
        elif "拒绝" in ai_response or "reject" in ai_response.lower() or "不同意" in ai_response:
            recommendation = "reject"
            risk_score = 8.0

        # 提取建议（查找列表格式的内容）
        import re
        suggestion_patterns = [
            r"建议[：:]\s*(.+)",
            r"[1-9]\.\s*(.+)",
            r"[-•]\s*(.+)"
        ]

        for pattern in suggestion_patterns:
            matches = re.findall(pattern, ai_response)
            if matches:
                suggestions.extend([match.strip() for match in matches[:3]])  # 最多3个建议
                break

        if not suggestions:
            suggestions = ["请仔细审查文档内容", "确认所有必要信息完整", "考虑相关政策规定"]

        return ApprovalResponse(
            recommendation=recommendation,
            reason=reason,
            risk_score=risk_score,
            suggestions=suggestions
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"审批分析失败: {str(e)}")

# 会议系统相关
class MeetingTranscriptRequest(BaseModel):
    transcript: str
    meeting_type: Optional[str] = "general"
    participants: Optional[List[str]] = None

class MeetingSummaryResponse(BaseModel):
    summary: str
    key_points: List[str]
    action_items: List[Dict[str, str]]
    decisions: List[str]

@router.post("/meeting/summarize", response_model=MeetingSummaryResponse)
async def summarize_meeting(request: MeetingTranscriptRequest):
    """会议摘要生成"""

    participants_text = ""
    if request.participants:
        participants_text = f"参会人员：{', '.join(request.participants)}"

    system_prompt = f"""
    你是一个专业的会议助手。请分析以下{request.meeting_type}会议转录内容，并提供：
    1. 会议摘要（200字以内）
    2. 关键讨论点（3-5个要点）
    3. 决策事项（明确的决定）
    4. 行动项目（待办事项，包括负责人和截止时间）

    {participants_text}

    请以结构化格式返回，确保信息准确简洁。
    """

    try:
        result = await call_qwen_api(
            prompt=request.transcript,
            system_prompt=system_prompt,
            max_tokens=1500
        )

        ai_response = result["response"]

        # 解析会议摘要
        summary_match = re.search(r"摘要[：:](.+?)(?=关键|要点|决策|$)", ai_response, re.DOTALL)
        summary = summary_match.group(1).strip() if summary_match else ai_response[:200]

        # 提取关键点
        key_points = []
        key_point_patterns = [
            r"关键.*?[：:]\s*(.+?)(?=决策|行动|$)",
            r"要点.*?[：:]\s*(.+?)(?=决策|行动|$)",
            r"讨论.*?[：:]\s*(.+?)(?=决策|行动|$)"
        ]

        for pattern in key_point_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                points_text = match.group(1)
                # 分割成列表
                points = re.findall(r"[1-9]\.\s*(.+?)(?=[1-9]\.|$)", points_text)
                if not points:
                    points = [line.strip() for line in points_text.split('\n') if line.strip()]
                key_points = points[:5]  # 最多5个要点
                break

        if not key_points:
            key_points = ["会议内容讨论", "相关议题分析", "参与者观点交流"]

        # 提取决策事项
        decisions = []
        decision_patterns = [
            r"决策[：:](.+?)(?=行动|$)",
            r"决定[：:](.+?)(?=行动|$)",
            r"结论[：:](.+?)(?=行动|$)"
        ]

        for pattern in decision_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                decision_text = match.group(1)
                decisions = [line.strip() for line in decision_text.split('\n') if line.strip()][:3]
                break

        if not decisions:
            decisions = ["待进一步讨论确定"]

        # 提取行动项目
        action_items = []
        action_patterns = [
            r"行动.*?[：:](.+?)$",
            r"待办.*?[：:](.+?)$",
            r"任务.*?[：:](.+?)$"
        ]

        for pattern in action_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                action_text = match.group(1)
                # 解析行动项目
                action_lines = [line.strip() for line in action_text.split('\n') if line.strip()]
                for line in action_lines[:3]:  # 最多3个行动项目
                    action_items.append({
                        "task": line,
                        "assignee": "待分配",
                        "deadline": "待确定"
                    })
                break

        if not action_items:
            action_items = [{"task": "跟进会议讨论内容", "assignee": "待分配", "deadline": "待确定"}]

        return MeetingSummaryResponse(
            summary=summary,
            key_points=key_points,
            action_items=action_items,
            decisions=decisions
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"会议摘要生成失败: {str(e)}")

# 教育系统相关
class HomeworkRequest(BaseModel):
    assignment: str
    student_answer: str
    subject: str
    grade_level: Optional[str] = None

class HomeworkResponse(BaseModel):
    score: float
    feedback: str
    suggestions: List[str]
    correct_answer: Optional[str] = None

@router.post("/education/grade", response_model=HomeworkResponse)
async def grade_homework(request: HomeworkRequest):
    """作业批改"""

    grade_level_text = request.grade_level or "中学"

    system_prompt = f"""
    你是一位专业的{request.subject}老师，正在批改{grade_level_text}学生的作业。

    作业题目：{request.assignment}
    学生答案：{request.student_answer}

    请提供：
    1. 分数（0-100分）
    2. 详细评语（包含优点和不足）
    3. 学习建议（3个具体建议）
    4. 参考答案（如果适用）

    请以教育性和鼓励性的语调进行评价，帮助学生改进。
    """

    try:
        result = await call_qwen_api(
            prompt=f"题目：{request.assignment}\n学生答案：{request.student_answer}",
            system_prompt=system_prompt,
            max_tokens=1200
        )

        ai_response = result["response"]

        # 提取分数
        score = 75.0  # 默认分数
        score_patterns = [
            r"分数[：:]?\s*(\d+)",
            r"得分[：:]?\s*(\d+)",
            r"(\d+)\s*分"
        ]

        for pattern in score_patterns:
            match = re.search(pattern, ai_response)
            if match:
                score = float(match.group(1))
                break

        # 提取评语
        feedback_patterns = [
            r"评语[：:](.+?)(?=建议|参考|$)",
            r"评价[：:](.+?)(?=建议|参考|$)",
            r"反馈[：:](.+?)(?=建议|参考|$)"
        ]

        feedback = ai_response  # 默认使用全部回复
        for pattern in feedback_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                feedback = match.group(1).strip()
                break

        # 提取建议
        suggestions = []
        suggestion_patterns = [
            r"建议[：:](.+?)(?=参考|$)",
            r"改进[：:](.+?)(?=参考|$)"
        ]

        for pattern in suggestion_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                suggestion_text = match.group(1)
                # 分割建议
                suggestions = [
                    line.strip()
                    for line in suggestion_text.split('\n')
                    if line.strip() and not line.strip().startswith('参考')
                ][:3]
                break

        if not suggestions:
            suggestions = [
                "继续保持学习热情",
                "多练习相关题型",
                "及时复习巩固知识点"
            ]

        # 提取参考答案
        correct_answer = None
        answer_patterns = [
            r"参考答案[：:](.+?)$",
            r"正确答案[：:](.+?)$",
            r"标准答案[：:](.+?)$"
        ]

        for pattern in answer_patterns:
            match = re.search(pattern, ai_response, re.DOTALL)
            if match:
                correct_answer = match.group(1).strip()
                break

        return HomeworkResponse(
            score=score,
            feedback=feedback,
            suggestions=suggestions,
            correct_answer=correct_answer
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"作业批改失败: {str(e)}")

# 邮件系统相关
class EmailRequest(BaseModel):
    subject: str
    content: str
    sender: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class EmailClassificationResponse(BaseModel):
    category: str  # "urgent", "normal", "spam", "promotion", etc.
    priority: int  # 1-5
    sentiment: str  # "positive", "negative", "neutral"
    suggested_response: Optional[str] = None

@router.post("/email/classify", response_model=EmailClassificationResponse)
async def classify_email(request: EmailRequest):
    """邮件分类和分析"""

    sender_text = f"发件人：{request.sender}" if request.sender else ""
    context_text = f"上下文：{request.context}" if request.context else ""

    system_prompt = f"""
    请分析以下邮件，并提供：
    1. 邮件类别（urgent/normal/spam/promotion/notification/personal/work）
    2. 优先级（1-5，5为最高）
    3. 情感倾向（positive/negative/neutral）
    4. 是否需要回复的建议

    {sender_text}
    {context_text}

    请根据邮件内容的紧急程度、重要性和发件人来判断。
    """

    email_content = f"主题：{request.subject}\n内容：{request.content}"

    try:
        result = await call_qwen_api(
            prompt=email_content,
            system_prompt=system_prompt,
            max_tokens=800
        )

        ai_response = result["response"]

        # 分析邮件类别
        category = "normal"  # 默认类别
        category_keywords = {
            "urgent": ["紧急", "急", "立即", "马上", "urgent", "asap"],
            "spam": ["垃圾", "广告", "推销", "spam", "promotion"],
            "work": ["工作", "项目", "会议", "报告", "work", "meeting"],
            "personal": ["个人", "私人", "朋友", "personal", "friend"],
            "notification": ["通知", "提醒", "notification", "reminder"]
        }

        for cat, keywords in category_keywords.items():
            if any(keyword in ai_response.lower() or keyword in request.subject.lower() or keyword in request.content.lower() for keyword in keywords):
                category = cat
                break

        # 确定优先级
        priority = 3  # 默认优先级
        if "紧急" in ai_response or "urgent" in ai_response.lower() or "急" in request.subject:
            priority = 5
        elif "重要" in ai_response or "important" in ai_response.lower():
            priority = 4
        elif "垃圾" in ai_response or "spam" in ai_response.lower():
            priority = 1

        # 分析情感倾向
        sentiment = "neutral"  # 默认情感
        positive_words = ["感谢", "谢谢", "好", "棒", "excellent", "great", "thank"]
        negative_words = ["问题", "错误", "不满", "投诉", "problem", "issue", "complaint"]

        content_lower = (request.subject + " " + request.content).lower()

        if any(word in content_lower for word in positive_words):
            sentiment = "positive"
        elif any(word in content_lower for word in negative_words):
            sentiment = "negative"

        # 生成回复建议
        suggested_response = None
        if priority >= 4 or category == "work":
            if sentiment == "negative":
                suggested_response = "建议及时回复，解决相关问题"
            else:
                suggested_response = "建议在24小时内回复"
        elif category == "spam":
            suggested_response = "建议删除或标记为垃圾邮件"
        else:
            suggested_response = "可以稍后回复或根据重要性决定"

        return EmailClassificationResponse(
            category=category,
            priority=priority,
            sentiment=sentiment,
            suggested_response=suggested_response
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"邮件分类失败: {str(e)}")

class EmailReplyRequest(BaseModel):
    original_email: str
    reply_type: str  # "formal", "casual", "brief"
    key_points: Optional[List[str]] = None

class EmailReplyResponse(BaseModel):
    reply_content: str
    tone: str
    suggestions: List[str]

@router.post("/email/reply", response_model=EmailReplyResponse)
async def generate_email_reply(request: EmailReplyRequest):
    """邮件回复生成"""

    key_points_text = ""
    if request.key_points:
        key_points_text = f"需要包含的要点：{', '.join(request.key_points)}"

    tone_mapping = {
        "formal": "正式",
        "casual": "随意",
        "brief": "简洁"
    }

    tone_chinese = tone_mapping.get(request.reply_type, "专业")

    system_prompt = f"""
    请为以下邮件生成一个{tone_chinese}的回复：

    原邮件：{request.original_email}
    {key_points_text}

    回复要求：
    1. 语调{tone_chinese}且礼貌
    2. 内容简洁明了
    3. 回应原邮件的关键问题
    4. 包含适当的开头和结尾
    5. 符合商务邮件规范

    请直接生成回复内容，不需要额外说明。
    """

    try:
        result = await call_qwen_api(
            prompt=request.original_email,
            system_prompt=system_prompt,
            max_tokens=1000
        )

        reply_content = result["response"].strip()

        # 生成改进建议
        suggestions = []

        if request.reply_type == "formal":
            suggestions = [
                "确保使用正式的称谓和敬语",
                "检查语法和拼写错误",
                "保持专业的语调"
            ]
        elif request.reply_type == "casual":
            suggestions = [
                "可以使用更轻松的语调",
                "适当添加友好的表达",
                "保持简洁明了"
            ]
        elif request.reply_type == "brief":
            suggestions = [
                "进一步精简内容",
                "突出关键信息",
                "避免冗余表达"
            ]
        else:
            suggestions = [
                "检查回复是否完整回应了原邮件",
                "确保语调适合收件人",
                "考虑添加后续行动建议"
            ]

        return EmailReplyResponse(
            reply_content=reply_content,
            tone=request.reply_type,
            suggestions=suggestions
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"邮件回复生成失败: {str(e)}")

# 导出路由
tasks_router = router
