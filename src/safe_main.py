#!/usr/bin/env python3
"""
安全的主程序 - 避免内存问题
"""

import os
import sys
import gc
import logging
from datetime import datetime
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn

# 设置内存优化
os.environ['PYTORCH_MPS_HIGH_WATERMARK_RATIO'] = '0.0'
os.environ['OMP_NUM_THREADS'] = '2'
os.environ['MKL_NUM_THREADS'] = '2'

# 强制垃圾回收
gc.collect()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="安全视频翻译API", version="1.0.0")

# 静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    return {"message": "安全视频翻译系统", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    import psutil
    
    memory = psutil.virtual_memory()
    return {
        "status": "healthy",
        "memory_available_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/translate_video_safe")
async def translate_video_safe(
    file: UploadFile = File(...),
    target_language: str = Form("zh"),
    source_language: str = Form("auto"),
    force_transcribe: bool = Form(False)
):
    """安全的视频翻译接口"""
    
    # 验证文件
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能为空")
    
    file_ext = file.filename.lower().split('.')[-1]
    supported_formats = ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'wmv']
    
    if file_ext not in supported_formats:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的视频格式。支持: {', '.join(supported_formats)}"
        )
    
    try:
        # 导入轻量级翻译器
        from integrations.lightweight_video_translator import LightweightVideoTranslator
        from integrations.stable_video_processor import SimpleVideoUploadHandler
        
        # 保存上传文件
        upload_handler = SimpleVideoUploadHandler()
        file_content = await file.read()
        video_path = await upload_handler.save_uploaded_file(file_content, file.filename)
        
        # 创建轻量级翻译器
        translator = LightweightVideoTranslator()
        
        # 检查依赖
        deps = translator.check_dependencies()
        
        missing_deps = []
        if not deps['ffmpeg']:
            missing_deps.append("ffmpeg")
        if not deps['whisper'] and force_transcribe:
            missing_deps.append("whisper")
        
        if missing_deps:
            raise HTTPException(
                status_code=500, 
                detail=f"缺少依赖: {', '.join(missing_deps)}"
            )
        
        # 处理视频
        result = await translator.process_video_lightweight(
            video_path, 
            target_language, 
            source_language,
            force_transcribe
        )
        
        # 清理
        translator.cleanup()
        
        # 强制垃圾回收
        gc.collect()
        
        if result.get("success"):
            return {
                "message": "视频翻译完成",
                "video_info": {
                    "filename": result["video_file"],
                    "size_mb": result["file_size_mb"],
                    "subtitle_source": result.get("subtitle_source", "unknown")
                },
                "translation_info": {
                    "source_language": result.get("detected_language", source_language),
                    "target_language": target_language
                },
                "processing_steps": result["processing_steps"],
                "subtitles": {
                    "original": result.get("original_subtitles", ""),
                    "translated": result.get("translated_subtitles", "")
                },
                "processed_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=500, detail=result.get("error", "视频翻译失败"))
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频翻译错误: {e}")
        # 强制垃圾回收
        gc.collect()
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """主函数"""
    # 从环境变量读取配置
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    
    logger.info(f"🌐 启动安全服务: http://{host}:{port}")
    logger.info(f"📖 API 文档: http://{host}:{port}/docs")
    logger.info(f"🎬 视频翻译: http://{host}:{port}/static/video_translator.html")
    
    uvicorn.run(
        "safe_main:app",
        host=host,
        port=port,
        reload=False,  # 禁用重载避免内存问题
        log_level="info",
        workers=1  # 单进程避免内存问题
    )

if __name__ == "__main__":
    main()
