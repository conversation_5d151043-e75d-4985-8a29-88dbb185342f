#!/usr/bin/env python3
"""
测试数学试卷批改
"""

import requests
import json

def test_math_paper():
    """测试数学试卷批改"""
    print("📝 测试数学试卷批改...")
    
    # 数学试卷内容（正确的JSON格式）
    math_paper = """以下是一份高中数学试卷，请批改并给出详细解答：

一、选择题（每题5分，共50分）

1. 已知集合A={1,2,3}，B={2,3,4}，则A∪B=（  ）
   A. {1,2,3,4}  B. {2,3}  C. {1,4}  D. {1,2,3,4,5}

2. 函数f(x)=2x+1的反函数是（  ）
   A. f^(-1)(x)=(x-1)/2  B. f^(-1)(x)=(x+1)/2  C. f^(-1)(x)=2x-1  D. f^(-1)(x)=x/2+1

3. 已知sin(α)=3/5，α为第二象限角，则cos(α)=（  ）
   A. 4/5  B. -4/5  C. 3/4  D. -3/4

4. 等差数列{an}中，a1=2，d=3，则a10=（  ）
   A. 29  B. 32  C. 35  D. 38

5. 已知向量a=(1,2)，b=(3,4)，则a·b=（  ）
   A. 7  B. 11  C. 5  D. 9

二、填空题（每题4分，共20分）

1. 已知函数f(x)=x²-2x+1，则f(3)=______。

2. 若log₂(x)=3，则x=______。

3. 已知等比数列{an}的首项a1=2，公比q=3，则a4=______。

4. 圆x²+y²=25的半径是______。

5. 已知复数z=3+4i，则|z|=______。

三、解答题（每题10分，共30分）

1. 解方程：x²-5x+6=0

2. 求函数f(x)=x³-3x²+2在区间[0,3]上的最大值和最小值。

3. 已知三角形ABC中，a=3，b=4，C=60°，求边c的长度。

请给出每题的正确答案和详细解题过程。"""
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": math_paper,
                "max_tokens": 1500,
                "temperature": 0.6
            },
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            tokens_used = data.get('tokens_used', 0)
            processing_time = data.get('processing_time', 0)
            
            print("✅ 数学试卷批改成功")
            print(f"   Tokens使用: {tokens_used}/1500")
            print(f"   处理时间: {processing_time:.1f}s")
            print(f"   回答长度: {len(response_text)} 字符")
            
            print("\n📋 批改结果:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)
            
            # 检查回答完整性
            has_all_sections = all(section in response_text for section in ['选择题', '填空题', '解答题'])
            print(f"\n✅ 包含所有题型: {'是' if has_all_sections else '否'}")
            
            # 检查是否有具体答案
            has_answers = any(char in response_text for char in ['A.', 'B.', 'C.', 'D.', '='])
            print(f"✅ 包含具体答案: {'是' if has_answers else '否'}")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_simple_math():
    """测试简单数学问题"""
    print("\n🧮 测试简单数学问题...")
    
    simple_questions = [
        "解方程：x²-5x+6=0",
        "计算：sin(30°)+cos(60°)",
        "求函数f(x)=x²的导数",
        "已知向量a=(1,2)，b=(3,4)，计算a·b"
    ]
    
    for i, question in enumerate(simple_questions, 1):
        print(f"\n问题 {i}: {question}")
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json={
                    "message": f"请详细解答这个数学问题：{question}",
                    "max_tokens": 300,
                    "temperature": 0.3
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)
                
                print(f"✅ 回答成功 ({tokens_used} tokens)")
                print(f"解答: {response_text}")
                
            else:
                print(f"❌ 失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")

def test_with_file():
    """使用文件测试（避免JSON转义问题）"""
    print("\n📁 使用文件测试...")
    
    # 创建测试文件
    test_content = """请批改以下数学题：

1. 计算：2+3×4-5 = ?
学生答案：9

2. 解方程：x+5=12
学生答案：x=7

3. 求函数f(x)=x²在x=3处的值
学生答案：f(3)=9

请给出正确答案并指出学生答案的对错。"""
    
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": test_content,
                "max_tokens": 500,
                "temperature": 0.5
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            
            print("✅ 文件测试成功")
            print("批改结果:")
            print("-" * 40)
            print(response_text)
            print("-" * 40)
            
        else:
            print(f"❌ 文件测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 文件测试错误: {e}")

def main():
    """主函数"""
    print("🧮 数学试卷批改测试")
    print("=" * 60)
    
    # 1. 测试完整数学试卷
    test_math_paper()
    
    # 2. 测试简单数学问题
    test_simple_math()
    
    # 3. 测试文件方式
    test_with_file()
    
    print("\n💡 使用建议:")
    print("1. 对于包含数学公式的内容，建议使用简化的文本格式")
    print("2. 避免在JSON中使用复杂的LaTeX公式")
    print("3. 可以分题目逐个批改，获得更详细的解答")
    print("4. 使用文件上传方式处理复杂的试卷内容")

if __name__ == "__main__":
    main()
