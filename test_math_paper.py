#!/usr/bin/env python3
"""
测试多学科试卷批改
"""

import requests
import json


def test_subject_paper(subject, paper_content):
    """测试指定学科试卷批改"""
    print(f"📝 测试{subject}试卷批改...")

    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": f"以下是一份{subject}试卷，请批改并给出详细解答：\n{paper_content}",
                "max_tokens": 1500,
                "temperature": 0.6
            },
            timeout=90
        )

        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            tokens_used = data.get('tokens_used', 0)
            processing_time = data.get('processing_time', 0)

            print("✅ 试卷批改成功")
            print(f"   Tokens使用: {tokens_used}/1500")
            print(f"   处理时间: {processing_time:.1f}s")
            print(f"   回答长度: {len(response_text)} 字符")

            print("\n📋 批改结果:")
            print("=" * 60)
            print(response_text)
            print("=" * 60)

            # 检查回答完整性
            has_all_sections = all(section in response_text for section in ['选择题', '填空题', '解答题'])
            print(f"\n✅ 包含所有题型: {'是' if has_all_sections else '否'}")

            # 检查是否有具体答案
            has_answers = any(char in response_text for char in ['A.', 'B.', 'C.', 'D.', '='])
            print(f"✅ 包含具体答案: {'是' if has_answers else '否'}")

        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")


def test_simple_subject(subject):
    """测试指定学科简单问题"""
    print(f"\n🧮 测试{subject}简单问题...")

    if subject == "高数":
        simple_questions = [
            "求函数 $f(x) = x^4 - 3x^3 + 2x - 1$ 的导数",
            "计算定积分 $\int_{1}^{2} (x^2 + 1)dx$",
            "求极限 $\lim_{x \to \infty} \frac{3x^2 + 2x}{2x^2 - 1}$",
            "判断级数 $\sum_{n = 1}^{\infty} \frac{(-1)^n}{n}$ 的敛散性"
        ]
    elif subject == "物理":
        simple_questions = [
            "根据万有引力定律，计算质量分别为 2kg 和 3kg 的两个物体，相距 1m 时的引力大小",
            "计算一个电容为 5μF 的电容器，在电压为 10V 时所带的电荷量",
            "简述安培定则的内容",
            "已知一个物体做匀加速直线运动，初速度为 2m/s，加速度为 1m/s²，求 5s 后的速度"
        ]
    elif subject == "语文":
        simple_questions = [
            "解释“天生我材必有用，千金散尽还复来”的含义",
            "分析老舍《骆驼祥子》中虎妞的人物形象",
            "请写出“元曲四大家”的名字",
            "简述《三国演义》中赤壁之战的主要情节"
        ]

    for i, question in enumerate(simple_questions, 1):
        print(f"\n问题 {i}: {question}")

        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json={
                    "message": f"请详细解答这个{subject}问题：{question}",
                    "max_tokens": 300,
                    "temperature": 0.3
                },
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)

                print(f"✅ 回答成功 ({tokens_used} tokens)")
                print(f"解答: {response_text}")

            else:
                print(f"❌ 失败: {response.status_code}")

        except Exception as e:
            print(f"❌ 错误: {e}")


def test_with_file(subject):
    """使用文件测试指定学科（避免JSON转义问题）"""
    print(f"\n📁 使用文件测试{subject}...")

    if subject == "高数":
        test_content = """请批改以下高数题：

1. 求函数 $f(x) = \sin(2x)$ 的导数，学生答案：$f'(x) = 2\cos(2x)$
2. 计算定积分 $\int_{0}^{\pi} \sin x dx$，学生答案：2
3. 求极限 $\lim_{x \to 0} \frac{\tan x}{x}$，学生答案：1

请给出正确答案并指出学生答案的对错。"""
    elif subject == "物理":
        test_content = """请批改以下物理题：

1. 一个质量为 4kg 的物体，在水平面上受到 8N 的拉力做匀速直线运动，求物体与水平面间的动摩擦因数，学生答案：0.2
2. 计算一个电阻为 10Ω 的导体，在通过 2A 电流时，其消耗的电功率，学生答案：40W
3. 简述电磁感应现象的定义，学生答案：闭合电路的一部分导体在磁场中做切割磁感线运动时，导体中就会产生电流

请给出正确答案并指出学生答案的对错。"""
    elif subject == "语文":
        test_content = """请批改以下语文题：

1. 解释“采菊东篱下，悠然见南山”，学生答案：在东篱之下采摘菊花，悠然间，那远处的南山映入眼帘。
2. 分析《水浒传》中林冲的人物形象，学生答案：林冲起初逆来顺受、委曲求全，后因高俅等人的迫害，最终被逼上梁山，成为反抗精神的代表。
3. 请写出“晚清四大谴责小说”的书名，学生答案：《官场现形记》《二十年目睹之怪现状》《老残游记》《孽海花》

请给出正确答案并指出学生答案的对错。"""

    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": test_content,
                "max_tokens": 500,
                "temperature": 0.5
            },
            timeout=60
        )

        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')

            print("✅ 文件测试成功")
            print("批改结果:")
            print("-" * 40)
            print(response_text)
            print("-" * 40)

        else:
            print(f"❌ 文件测试失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 文件测试错误: {e}")


def main():
    """主函数"""
    print("🧮 多学科试卷批改测试")
    print("=" * 60)

    # 新的高数试卷内容
    advanced_math_paper = """
一、选择题（每题 5 分，共 50 分）
1. 函数 $y = \cos(3x)$ 的导数为（  ）
   A. $-3\sin(3x)$  B. $3\sin(3x)$  C. $-\sin(3x)$  D. $\sin(3x)$
2. 极限 $\lim_{x \to 0} \frac{\ln(1 + x)}{x}$ 的值为（  ）
   A. 0  B. 1  C. 2  D. $\infty$
3. 定积分 $\int_{-1}^{1} x^3 dx$ 的值为（  ）
   A. 0  B. 1  C. 2  D. -2
4. 函数 $f(x) = x^3 - 3x^2 + 1$ 的极大值点为（  ）
   A. $x = 0$  B. $x = 1$  C. $x = 2$  D. $x = 3$
5. 设函数 $z = x^2y + xy^2$，则 $\frac{\partial z}{\partial x}$ 在点 $(1, 1)$ 处的值为（  ）
   A. 1  B. 2  C. 3  D. 4

二、填空题（每题 4 分，共 20 分）
1. 函数 $y = e^{-x}$ 的不定积分 $\int e^{-x}dx =$______。
2. 曲线 $y = x^3 - 2x$ 在点 $(1, -1)$ 处的切线斜率为______。
3. 若级数 $\sum_{n = 1}^{\infty} a_n$ 收敛，$\sum_{n = 1}^{\infty} b_n$ 发散，则 $\sum_{n = 1}^{\infty} (a_n + b_n)$ 是______（填“收敛”或“发散”）的。
4. 微分方程 $y' + 2y = 0$ 的通解为 $y =$______。
5. 设向量 $\vec{a}=(1, -2, 3)$，$\vec{b}=(2, 1, -1)$，则 $\vec{a} \cdot \vec{b} =$______。

三、解答题（每题 10 分，共 30 分）
1. 求函数 $y = x^2 - 4x + 5$ 在区间 $[0, 3]$ 上的最大值和最小值。
2. 计算二重积分 $\iint_D (x + y)dxdy$，其中 $D$ 是由 $x = 0$，$y = 0$ 和 $x + y = 1$ 所围成的区域。
3. 求微分方程 $y'' - 5y' + 6y = 0$ 的通解。

请给出每题的正确答案和详细解题过程。
"""

    subjects = ["高数", "物理", "语文"]
    for subject in subjects:
        if subject == "高数":
            test_subject_paper(subject, advanced_math_paper)
        else:
            # 可添加对应学科试卷内容
            test_subject_paper(subject, "")
        test_simple_subject(subject)
        test_with_file(subject)

    print("\n💡 使用建议:")
    print("1. 对于包含学科公式的内容，建议使用简化的文本格式")
    print("2. 避免在 JSON 中使用复杂的 LaTeX 公式")
    print("3. 可以分题目逐个批改，获得更详细的解答")
    print("4. 使用文件上传方式处理复杂的试卷内容")


if __name__ == "__main__":
    main()