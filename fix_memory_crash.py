#!/usr/bin/env python3
"""
修复内存崩溃问题
"""

import os
import sys
import subprocess
import psutil
import signal
import time

def kill_all_python_processes():
    """杀死所有相关的Python进程"""
    print("🔪 终止所有相关进程...")
    
    current_pid = os.getpid()
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] in ['Python', 'python', 'python3']:
                if proc.info['pid'] != current_pid:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if any(keyword in cmdline for keyword in ['main.py', 'uvicorn', 'fastapi', 'llama', 'whisper']):
                        print(f"终止进程: {proc.info['pid']} - {cmdline}")
                        try:
                            proc.terminate()
                            proc.wait(timeout=5)
                        except:
                            proc.kill()
                        killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
            continue
    
    print(f"✅ 终止了 {killed_count} 个进程")

def clear_cache_and_temp():
    """清理缓存和临时文件"""
    print("🧹 清理缓存和临时文件...")
    
    # 清理Python缓存
    cache_dirs = [
        "src/__pycache__",
        "src/api/__pycache__",
        "src/integrations/__pycache__",
        "src/models/__pycache__",
        "cache"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                import shutil
                shutil.rmtree(cache_dir)
                print(f"清理: {cache_dir}")
            except Exception as e:
                print(f"清理失败 {cache_dir}: {e}")
    
    # 清理临时文件
    import tempfile
    temp_dir = tempfile.gettempdir()
    
    try:
        for item in os.listdir(temp_dir):
            if any(keyword in item for keyword in ['video_translator', 'whisper', 'ffmpeg']):
                item_path = os.path.join(temp_dir, item)
                try:
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
                    print(f"清理临时文件: {item}")
                except:
                    pass
    except:
        pass
    
    print("✅ 缓存清理完成")

def check_and_fix_dependencies():
    """检查和修复依赖"""
    print("🔧 检查依赖...")
    
    # 检查FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg正常")
        else:
            print("❌ FFmpeg异常")
    except:
        print("❌ FFmpeg未找到")
    
    # 检查Python包
    packages = ['fastapi', 'uvicorn', 'psutil']
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package}正常")
        except ImportError:
            print(f"❌ {package}未安装")
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package}安装成功")
            except:
                print(f"❌ {package}安装失败")

def create_minimal_test():
    """创建最小测试"""
    test_content = '''#!/usr/bin/env python3
"""
最小测试程序
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI(title="最小测试API")

# 静态文件
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

@app.get("/")
async def root():
    return {"message": "最小测试系统", "status": "running"}

@app.get("/test")
async def test():
    import psutil
    memory = psutil.virtual_memory()
    return {
        "memory_available_gb": round(memory.available / (1024**3), 2),
        "memory_percent": memory.percent
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
'''
    
    with open("minimal_test.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 创建最小测试: minimal_test.py")

def main():
    """主函数"""
    print("🛠️  内存崩溃修复工具")
    print("=" * 40)
    
    # 1. 杀死所有相关进程
    kill_all_python_processes()
    
    # 等待进程完全终止
    time.sleep(2)
    
    # 2. 清理缓存
    clear_cache_and_temp()
    
    # 3. 检查依赖
    check_and_fix_dependencies()
    
    # 4. 创建最小测试
    create_minimal_test()
    
    print("\n🎯 修复完成！")
    print("\n📋 下一步:")
    print("1. 测试最小系统: python minimal_test.py")
    print("2. 如果正常，使用安全启动: python start_safe_video_translator.py")
    print("3. 访问: http://localhost:8000/test")
    
    print("\n💡 建议:")
    print("- 关闭其他占用内存的应用")
    print("- 使用较小的视频文件测试")
    print("- 避免同时运行多个Python进程")

if __name__ == "__main__":
    main()
