#!/bin/bash

# 快速启动脚本 - 跳过 llama.cpp 编译，直接使用 Python 库

set -e

echo "🚀 Qwen LLM Platform 快速启动"
echo "============================="

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行: ./scripts/setup_mac.sh"
    exit 1
fi

echo "🐍 激活虚拟环境..."
source venv/bin/activate

# 检查模型文件
if [ ! -f "models/model_config.json" ]; then
    echo "❌ 模型配置不存在，请先下载模型"
    exit 1
fi

# 检查模型文件是否有效
MODEL_FILE=$(jq -r '.model_file' models/model_config.json 2>/dev/null || echo "")
if [ -z "$MODEL_FILE" ] || [ ! -f "models/$MODEL_FILE" ]; then
    echo "❌ 模型文件不存在或无效"
    echo "🔄 重新下载模型..."
    ./scripts/download_model_alternative.sh
fi

# 检查模型文件大小
MODEL_SIZE=$(stat -f%z "models/$MODEL_FILE" 2>/dev/null || echo "0")
if [ "$MODEL_SIZE" -lt 1000000 ]; then
    echo "❌ 模型文件损坏或不完整 (大小: $MODEL_SIZE bytes)"
    echo "🔄 重新下载模型..."
    rm -f "models/$MODEL_FILE"
    ./scripts/download_model_alternative.sh
fi

# 安装必要的依赖
echo "📦 检查依赖..."
pip install httpx fastapi uvicorn pydantic llama-cpp-python --quiet

echo "✅ 环境检查完成"
echo ""
echo "🎯 启动选项："
echo "1) 启动 API 服务"
echo "2) 运行功能测试"
echo "3) 运行 API 示例"
echo "4) 查看模型信息"
echo ""

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "🚀 启动 API 服务..."
        echo "📖 API 文档: http://localhost:8000/docs"
        echo "🔍 健康检查: http://localhost:8000/health"
        echo ""
        echo "按 Ctrl+C 停止服务"
        echo ""
        
        cd src
        python main.py
        ;;
    
    2)
        echo "🧪 运行功能测试..."
        echo "⏳ 启动服务..."
        
        cd src
        python main.py &
        SERVER_PID=$!
        
        echo "📝 服务进程 ID: $SERVER_PID"
        sleep 10
        
        cd ..
        echo "🔍 运行测试..."
        python scripts/test_local.py
        
        echo "🛑 停止服务..."
        kill $SERVER_PID 2>/dev/null || true
        ;;
    
    3)
        echo "📚 运行 API 示例..."
        echo "⏳ 启动服务..."
        
        cd src
        python main.py &
        SERVER_PID=$!
        
        echo "📝 服务进程 ID: $SERVER_PID"
        sleep 10
        
        cd ..
        echo "🎭 运行示例..."
        python examples/api_usage_examples.py
        
        echo "🛑 停止服务..."
        kill $SERVER_PID 2>/dev/null || true
        ;;
    
    4)
        echo "📊 模型信息:"
        if [ -f "models/model_config.json" ]; then
            echo "配置文件内容:"
            cat models/model_config.json | jq .
            echo ""
            echo "模型文件:"
            ls -lh models/*.gguf 2>/dev/null || echo "未找到模型文件"
        else
            echo "❌ 模型配置文件不存在"
        fi
        ;;
    
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
