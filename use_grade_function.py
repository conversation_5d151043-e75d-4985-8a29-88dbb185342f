#!/usr/bin/env python3
"""
使用教育集成的批改功能
"""

import asyncio
import sys
import os

# 添加 src 路径
sys.path.insert(0, 'src')

from integrations.education_integration import EducationIntegration, IntelligentQA

async def test_math_grading():
    """测试数学试卷批改"""
    print("📝 使用教育集成批改数学试卷")
    print("=" * 60)
    
    edu = EducationIntegration()
    
    # 数学试卷内容
    math_assignment = """
    高中数学试卷：
    
    一、选择题（每题5分）
    1. 已知log₂a + log₂b = 1，则3^a + 9^b的最小值为（  ）
       A. 6    B. 6√3    C. 18    D. 24√3
    
    2. 函数f(x)=x³-3x的极值点是（  ）
       A. x=1    B. x=-1    C. x=±1    D. 无极值点
    
    二、填空题（每题4分）
    1. 若tanα=2，则(sinα+cosα)/(sinα-cosα)的值为______
    
    三、解答题（每题10分）
    1. 计算：log₂3·log₃4·log₄5·log₅6·log₆7·log₇8
    2. 已知复数z=(2+i)/(1-i)，求|z|和z的共轭复数
    """
    
    # 学生答案
    student_answer = """
    一、选择题
    1. A
    2. C
    
    二、填空题
    1. 3
    
    三、解答题
    1. 解：使用换底公式，原式=log₂8=3
    2. 解：z=(2+i)(1+i)/((1-i)(1+i))=(2+i+2i+i²)/(1-i²)=(2+3i-1)/(1+1)=(1+3i)/2
       所以|z|=√(1/4+9/4)=√(10/4)=√10/2
       共轭复数为(1-3i)/2
    """
    
    try:
        result = await edu.grade_assignment(
            assignment_text=math_assignment,
            student_answer=student_answer,
            subject="数学",
            grade_level="高中"
        )
        
        if "error" in result:
            print(f"❌ 批改失败: {result['error']}")
        else:
            print("✅ 批改成功")
            print(f"科目: {result['subject']}")
            print(f"年级: {result['grade_level']}")
            print(f"处理时间: {result['processing_time']:.2f}s")
            print(f"批改时间: {result['graded_at']}")
            
            print("\n📋 详细批改结果:")
            print("=" * 60)
            print(result['grading_result'])
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 批改异常: {e}")

async def test_individual_problems():
    """测试单个问题批改"""
    print("\n🔢 测试单个问题批改")
    print("=" * 60)
    
    edu = EducationIntegration()
    
    problems = [
        {
            "assignment": "解方程：x²-5x+6=0",
            "answer": "解：x²-5x+6=0，分解因式得(x-2)(x-3)=0，所以x=2或x=3",
            "subject": "数学"
        },
        {
            "assignment": "请解释牛顿第一定律",
            "answer": "牛顿第一定律说物体在没有外力作用时会保持静止或匀速直线运动状态。这叫做惯性定律。",
            "subject": "物理"
        },
        {
            "assignment": "分析《红楼梦》中林黛玉的性格特点",
            "answer": "林黛玉性格敏感多疑，才华横溢但体弱多病，对爱情专一但结局悲惨。她代表了封建社会中才女的悲剧命运。",
            "subject": "语文"
        }
    ]
    
    for i, problem in enumerate(problems, 1):
        print(f"\n问题 {i}: {problem['subject']}")
        print(f"题目: {problem['assignment']}")
        print(f"学生答案: {problem['answer']}")
        
        try:
            result = await edu.grade_assignment(
                assignment_text=problem['assignment'],
                student_answer=problem['answer'],
                subject=problem['subject'],
                grade_level="高中"
            )
            
            if "error" in result:
                print(f"❌ 批改失败: {result['error']}")
            else:
                print("✅ 批改结果:")
                print("-" * 40)
                print(result['grading_result'])
                print("-" * 40)
                
        except Exception as e:
            print(f"❌ 批改异常: {e}")
        
        await asyncio.sleep(1)  # 间隔1秒

async def test_intelligent_qa():
    """测试智能答疑"""
    print("\n🤖 测试智能答疑")
    print("=" * 60)
    
    qa = IntelligentQA()
    
    questions = [
        {
            "question": "为什么log₂3·log₃4·log₄5·log₅6·log₆7·log₇8等于3？",
            "subject": "数学",
            "context": "我们在学习对数的运算法则"
        },
        {
            "question": "复数的模长怎么计算？",
            "subject": "数学",
            "context": "刚学了复数的基本概念"
        },
        {
            "question": "三角函数中tanα=2时，怎么求其他三角函数值？",
            "subject": "数学",
            "context": "正在复习三角恒等式"
        }
    ]
    
    for i, q in enumerate(questions, 1):
        print(f"\n问题 {i}: {q['question']}")
        
        try:
            answer = await qa.answer_student_question(
                question=q['question'],
                subject=q['subject'],
                context=q['context']
            )
            
            if "error" in answer:
                print(f"❌ 答疑失败: {answer['error']}")
            else:
                print("✅ 答疑结果:")
                print("-" * 40)
                print(answer['answer'])
                print("-" * 40)
                print(f"置信度: {answer['confidence']}")
                
        except Exception as e:
            print(f"❌ 答疑异常: {e}")
        
        await asyncio.sleep(1)

async def test_quiz_generation():
    """测试题目生成"""
    print("\n📝 测试题目生成")
    print("=" * 60)
    
    edu = EducationIntegration()
    
    try:
        quiz_result = await edu.generate_quiz_questions(
            topic="二次函数",
            difficulty="中等",
            question_count=3,
            question_type="选择题"
        )
        
        if "error" in quiz_result:
            print(f"❌ 题目生成失败: {quiz_result['error']}")
        else:
            print("✅ 题目生成成功")
            print(f"主题: {quiz_result['topic']}")
            print(f"难度: {quiz_result['difficulty']}")
            print(f"题目数量: {quiz_result['question_count']}")
            print(f"题目类型: {quiz_result['question_type']}")
            
            print("\n📋 生成的题目:")
            print("=" * 60)
            print(quiz_result['questions'])
            print("=" * 60)
            
    except Exception as e:
        print(f"❌ 题目生成异常: {e}")

async def main():
    """主函数"""
    print("🎓 教育集成功能测试")
    print("=" * 80)
    
    # 检查服务状态
    import httpx
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                print("✅ Qwen 服务运行正常")
            else:
                print(f"⚠️  Qwen 服务状态异常: {response.status_code}")
                return
    except Exception as e:
        print(f"❌ 无法连接 Qwen 服务: {e}")
        print("请确保服务正在运行: python fast_start.py")
        return
    
    # 运行测试
    await test_math_grading()
    await test_individual_problems()
    await test_intelligent_qa()
    await test_quiz_generation()
    
    print("\n🎉 教育集成功能测试完成")
    print("\n💡 使用建议:")
    print("1. grade_assignment() 适合批改作业和试卷")
    print("2. answer_student_question() 适合智能答疑")
    print("3. generate_quiz_questions() 适合生成练习题")
    print("4. 所有功能都支持多学科（数学、物理、语文等）")

if __name__ == "__main__":
    asyncio.run(main())
