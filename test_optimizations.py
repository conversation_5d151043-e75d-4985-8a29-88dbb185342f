#!/usr/bin/env python3
"""
测试缓存和并行处理优化效果
"""

import asyncio
import time
import requests
import json
from concurrent.futures import ThreadPoolExecutor
import statistics

class OptimizationTester:
    """优化效果测试器"""
    
    def __init__(self, api_url="http://localhost:8000"):
        self.api_url = api_url
        
    def test_cache_effectiveness(self):
        """测试缓存效果"""
        print("🧪 测试缓存效果")
        print("=" * 50)
        
        # 相同的请求
        test_request = {
            "message": "什么是人工智能？",
            "max_tokens": 50,
            "temperature": 0.3
        }
        
        times = []
        
        # 第一次请求（无缓存）
        print("第一次请求（无缓存）...")
        start_time = time.time()
        response1 = requests.post(f"{self.api_url}/chat", json=test_request)
        time1 = time.time() - start_time
        times.append(time1)
        
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"✅ 响应时间: {time1:.2f}s")
            print(f"📝 回复: {data1['response'][:50]}...")
            
            # 立即第二次请求（应该命中缓存）
            print("\n第二次请求（应该命中缓存）...")
            start_time = time.time()
            response2 = requests.post(f"{self.api_url}/chat", json=test_request)
            time2 = time.time() - start_time
            times.append(time2)
            
            if response2.status_code == 200:
                data2 = response2.json()
                print(f"✅ 响应时间: {time2:.2f}s")
                print(f"📝 回复: {data2['response'][:50]}...")
                
                # 检查是否来自缓存
                if data2.get('from_cache'):
                    print("💾 ✅ 缓存命中！")
                else:
                    print("⚠️  未检测到缓存标记")
                
                # 计算加速比
                speedup = time1 / time2 if time2 > 0 else float('inf')
                print(f"🚀 加速比: {speedup:.1f}x")
                
                if speedup > 2:
                    print("🎉 缓存效果显著！")
                elif speedup > 1.2:
                    print("✅ 缓存有效")
                else:
                    print("⚠️  缓存效果不明显")
            else:
                print(f"❌ 第二次请求失败: {response2.status_code}")
        else:
            print(f"❌ 第一次请求失败: {response1.status_code}")
        
        return times
    
    def test_parallel_processing(self):
        """测试并行处理效果"""
        print("\n🔄 测试并行处理效果")
        print("=" * 50)
        
        # 不同的请求
        test_requests = [
            {"message": "你好", "max_tokens": 10},
            {"message": "今天天气如何？", "max_tokens": 20},
            {"message": "解释一下机器学习", "max_tokens": 30},
            {"message": "推荐一本好书", "max_tokens": 25}
        ]
        
        # 串行测试
        print("串行处理测试...")
        serial_start = time.time()
        serial_times = []
        
        for i, req in enumerate(test_requests):
            start = time.time()
            response = requests.post(f"{self.api_url}/chat", json=req)
            elapsed = time.time() - start
            serial_times.append(elapsed)
            
            if response.status_code == 200:
                print(f"  请求 {i+1}: {elapsed:.2f}s ✅")
            else:
                print(f"  请求 {i+1}: {elapsed:.2f}s ❌")
        
        serial_total = time.time() - serial_start
        print(f"串行总时间: {serial_total:.2f}s")
        
        # 并行测试
        print("\n并行处理测试...")
        parallel_start = time.time()
        
        def make_request(req_data):
            start = time.time()
            try:
                response = requests.post(f"{self.api_url}/chat", json=req_data[1], timeout=30)
                elapsed = time.time() - start
                return req_data[0], elapsed, response.status_code == 200
            except Exception as e:
                elapsed = time.time() - start
                return req_data[0], elapsed, False
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(make_request, (i, req)) for i, req in enumerate(test_requests)]
            parallel_results = [future.result() for future in futures]
        
        parallel_total = time.time() - parallel_start
        
        for req_id, elapsed, success in parallel_results:
            status = "✅" if success else "❌"
            print(f"  请求 {req_id+1}: {elapsed:.2f}s {status}")
        
        print(f"并行总时间: {parallel_total:.2f}s")
        
        # 计算效率
        speedup = serial_total / parallel_total if parallel_total > 0 else 0
        efficiency = speedup / len(test_requests) * 100
        
        print(f"\n📊 并行处理效果:")
        print(f"   加速比: {speedup:.1f}x")
        print(f"   效率: {efficiency:.1f}%")
        
        if speedup > 1.5:
            print("🎉 并行处理效果显著！")
        elif speedup > 1.1:
            print("✅ 并行处理有效")
        else:
            print("⚠️  并行处理效果不明显")
        
        return serial_total, parallel_total
    
    def test_mixed_workload(self):
        """测试混合工作负载"""
        print("\n🎯 测试混合工作负载（缓存 + 并行）")
        print("=" * 50)
        
        # 混合请求：一些重复（缓存），一些新的（并行）
        mixed_requests = [
            {"message": "什么是人工智能？", "max_tokens": 50},  # 重复请求
            {"message": "今天是星期几？", "max_tokens": 10},     # 新请求
            {"message": "什么是人工智能？", "max_tokens": 50},  # 重复请求（缓存）
            {"message": "推荐一部电影", "max_tokens": 30},       # 新请求
            {"message": "今天是星期几？", "max_tokens": 10},     # 重复请求（缓存）
            {"message": "解释量子计算", "max_tokens": 40}        # 新请求
        ]
        
        print("发送混合工作负载...")
        start_time = time.time()
        
        def make_mixed_request(req_data):
            req_id, req = req_data
            start = time.time()
            try:
                response = requests.post(f"{self.api_url}/chat", json=req, timeout=30)
                elapsed = time.time() - start
                
                if response.status_code == 200:
                    data = response.json()
                    from_cache = data.get('from_cache', False)
                    return req_id, elapsed, True, from_cache
                else:
                    return req_id, elapsed, False, False
            except Exception:
                elapsed = time.time() - start
                return req_id, elapsed, False, False
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(make_mixed_request, (i, req)) for i, req in enumerate(mixed_requests)]
            results = [future.result() for future in futures]
        
        total_time = time.time() - start_time
        
        cache_hits = 0
        successful_requests = 0
        response_times = []
        
        for req_id, elapsed, success, from_cache in results:
            status = "✅" if success else "❌"
            cache_status = "💾" if from_cache else "🔄"
            print(f"  请求 {req_id+1}: {elapsed:.2f}s {status} {cache_status}")
            
            if success:
                successful_requests += 1
                response_times.append(elapsed)
                if from_cache:
                    cache_hits += 1
        
        print(f"\n📊 混合工作负载结果:")
        print(f"   总时间: {total_time:.2f}s")
        print(f"   成功请求: {successful_requests}/{len(mixed_requests)}")
        print(f"   缓存命中: {cache_hits}/{successful_requests}")
        print(f"   缓存命中率: {cache_hits/successful_requests*100:.1f}%")
        
        if response_times:
            print(f"   平均响应时间: {statistics.mean(response_times):.2f}s")
            print(f"   最快响应: {min(response_times):.2f}s")
            print(f"   最慢响应: {max(response_times):.2f}s")
        
        return total_time, cache_hits, successful_requests
    
    def get_system_stats(self):
        """获取系统统计"""
        try:
            response = requests.get(f"{self.api_url}/stats", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            return {"error": str(e)}
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 Qwen LLM Platform 优化效果综合测试")
        print("=" * 60)
        
        # 检查服务状态
        try:
            response = requests.get(f"{self.api_url}/health", timeout=10)
            if response.status_code != 200:
                print("❌ 服务不可用")
                return
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            return
        
        print("✅ 服务连接成功")
        
        # 获取初始统计
        print("\n📊 初始系统状态:")
        initial_stats = self.get_system_stats()
        if "error" not in initial_stats:
            if "model" in initial_stats:
                model_stats = initial_stats["model"]
                print(f"   总请求数: {model_stats.get('total_requests', 0)}")
                print(f"   缓存命中率: {model_stats.get('cache_hit_rate', '0%')}")
                print(f"   并行使用率: {model_stats.get('parallel_usage', '0%')}")
        
        # 运行测试
        cache_times = self.test_cache_effectiveness()
        serial_time, parallel_time = self.test_parallel_processing()
        mixed_time, cache_hits, total_requests = self.test_mixed_workload()
        
        # 获取最终统计
        print("\n📊 最终系统状态:")
        final_stats = self.get_system_stats()
        if "error" not in final_stats and "model" in final_stats:
            model_stats = final_stats["model"]
            print(f"   总请求数: {model_stats.get('total_requests', 0)}")
            print(f"   缓存命中率: {model_stats.get('cache_hit_rate', '0%')}")
            print(f"   并行使用率: {model_stats.get('parallel_usage', '0%')}")
            print(f"   平均响应时间: {model_stats.get('average_response_time', '0s')}")
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 优化效果总结:")
        
        if len(cache_times) >= 2:
            cache_speedup = cache_times[0] / cache_times[1] if cache_times[1] > 0 else 0
            print(f"   缓存加速比: {cache_speedup:.1f}x")
        
        parallel_speedup = serial_time / parallel_time if parallel_time > 0 else 0
        print(f"   并行加速比: {parallel_speedup:.1f}x")
        
        cache_hit_rate = cache_hits / total_requests * 100 if total_requests > 0 else 0
        print(f"   混合负载缓存命中率: {cache_hit_rate:.1f}%")
        
        print("\n💡 优化建议:")
        if cache_speedup < 2:
            print("   - 考虑增加缓存大小或调整TTL")
        if parallel_speedup < 1.5:
            print("   - 考虑增加并行处理池大小")
        if cache_hit_rate < 30:
            print("   - 优化缓存策略，提高命中率")
        
        if cache_speedup >= 2 and parallel_speedup >= 1.5:
            print("   🎉 优化效果优秀！")

if __name__ == "__main__":
    tester = OptimizationTester()
    tester.run_comprehensive_test()
