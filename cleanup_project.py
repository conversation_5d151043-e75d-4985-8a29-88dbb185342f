#!/usr/bin/env python3
"""
项目清理脚本 - 删除不必要的文件
"""

import os
import shutil
import glob
from pathlib import Path

class ProjectCleaner:
    """项目清理器"""
    
    def __init__(self):
        self.project_root = os.getcwd()
        self.deleted_files = []
        self.deleted_dirs = []
        self.kept_files = []
        
    def scan_files(self):
        """扫描项目文件"""
        print("🔍 扫描项目文件...")
        
        # 需要删除的文件类型
        unnecessary_files = [
            # 测试和调试文件
            "debug_paths.py",
            "simple_fix.py", 
            "test_local.py",
            "test_optimizations.py",
            "test_startup_speed.py",
            "quick_fix.py",
            "fix_empty_response.py",
            
            # 优化配置文件（保留最终版本）
            "optimize_performance.py",
            "extreme_optimize.py",
            "advanced_optimize.py",
            
            # 示例和演示文件
            "integration_examples.py",
            "daily_tools.py",
            "translation_test.py",
            
            # 临时文件
            "warmup_model.py",
            "speed_test.py",
            "performance_monitor.py",
            "quick_performance_test.py",
            
            # 重复的模型文件
            "ultra_fast_model.py",
            "fast_model.py",
            
            # Webhook 示例
            "webhook_integration.py",
            
            # 其他临时文件
            "*.tmp",
            "*.log",
            "*.bak",
            "__pycache__",
            "*.pyc",
            ".DS_Store"
        ]
        
        # 需要保留的核心文件
        essential_files = [
            # 核心启动文件
            "start_from_root.py",
            "start_optimized.sh", 
            "fast_start.py",
            "fast_optimize.py",
            
            # 主要源码
            "src/main.py",
            "src/models/qwen_model.py",
            "src/models/optimized_qwen_model.py",
            "src/models/minimal_qwen_model.py",
            "src/cache/smart_cache.py",
            "src/parallel/lightweight_pool.py",
            "src/parallel/model_pool.py",
            "src/api/",
            
            # 配置和脚本
            "models/model_config.json",
            "scripts/",
            "requirements.txt",
            "README.md",
            
            # 虚拟环境和模型
            "venv/",
            "models/*.gguf"
        ]
        
        return unnecessary_files, essential_files
    
    def remove_file(self, filepath):
        """删除文件"""
        try:
            if os.path.isfile(filepath):
                os.remove(filepath)
                self.deleted_files.append(filepath)
                print(f"🗑️  删除文件: {filepath}")
                return True
            elif os.path.isdir(filepath):
                shutil.rmtree(filepath)
                self.deleted_dirs.append(filepath)
                print(f"🗑️  删除目录: {filepath}")
                return True
        except Exception as e:
            print(f"❌ 删除失败 {filepath}: {e}")
            return False
        return False
    
    def clean_pycache(self):
        """清理 Python 缓存文件"""
        print("\n🧹 清理 Python 缓存...")
        
        # 查找所有 __pycache__ 目录
        pycache_dirs = []
        for root, dirs, files in os.walk(self.project_root):
            if "__pycache__" in dirs:
                pycache_dirs.append(os.path.join(root, "__pycache__"))
        
        for pycache_dir in pycache_dirs:
            if self.remove_file(pycache_dir):
                pass
        
        # 删除 .pyc 文件
        pyc_files = glob.glob("**/*.pyc", recursive=True)
        for pyc_file in pyc_files:
            self.remove_file(pyc_file)
    
    def clean_temporary_files(self):
        """清理临时文件"""
        print("\n🧹 清理临时文件...")
        
        temp_patterns = [
            "*.tmp",
            "*.log", 
            "*.bak",
            ".DS_Store",
            "Thumbs.db",
            "*.swp",
            "*~"
        ]
        
        for pattern in temp_patterns:
            temp_files = glob.glob(pattern, recursive=True)
            for temp_file in temp_files:
                self.remove_file(temp_file)
    
    def clean_test_files(self):
        """清理测试文件"""
        print("\n🧹 清理测试和调试文件...")
        
        test_files = [
            "debug_paths.py",
            "simple_fix.py",
            "test_local.py", 
            "test_optimizations.py",
            "test_startup_speed.py",
            "quick_fix.py",
            "fix_empty_response.py",
            "integration_examples.py",
            "daily_tools.py",
            "translation_test.py",
            "warmup_model.py",
            "speed_test.py",
            "performance_monitor.py",
            "quick_performance_test.py",
            "webhook_integration.py"
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                self.remove_file(test_file)
    
    def clean_duplicate_files(self):
        """清理重复文件"""
        print("\n🧹 清理重复和过时文件...")
        
        duplicate_files = [
            "ultra_fast_model.py",
            "fast_model.py",
            "optimize_performance.py",
            "extreme_optimize.py", 
            "advanced_optimize.py"
        ]
        
        for dup_file in duplicate_files:
            if os.path.exists(dup_file):
                self.remove_file(dup_file)
    
    def organize_remaining_files(self):
        """整理剩余文件"""
        print("\n📁 整理项目结构...")
        
        # 确保目录结构正确
        essential_dirs = [
            "src",
            "src/models", 
            "src/cache",
            "src/parallel",
            "src/api",
            "scripts",
            "models",
            "cache"
        ]
        
        for dir_path in essential_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                print(f"📁 创建目录: {dir_path}")
    
    def show_summary(self):
        """显示清理总结"""
        print("\n" + "=" * 60)
        print("🎉 项目清理完成")
        print("=" * 60)
        
        print(f"🗑️  删除文件数: {len(self.deleted_files)}")
        print(f"🗑️  删除目录数: {len(self.deleted_dirs)}")
        
        if self.deleted_files:
            print("\n删除的文件:")
            for file in self.deleted_files:
                print(f"  - {file}")
        
        if self.deleted_dirs:
            print("\n删除的目录:")
            for dir in self.deleted_dirs:
                print(f"  - {dir}")
        
        # 显示保留的核心文件
        print("\n✅ 保留的核心文件:")
        core_files = [
            "src/main.py",
            "src/models/optimized_qwen_model.py", 
            "src/cache/smart_cache.py",
            "src/parallel/lightweight_pool.py",
            "fast_start.py",
            "fast_optimize.py",
            "start_optimized.sh",
            "models/model_config.json"
        ]
        
        for core_file in core_files:
            if os.path.exists(core_file):
                print(f"  ✅ {core_file}")
            else:
                print(f"  ❌ {core_file} (缺失)")
    
    def create_gitignore(self):
        """创建 .gitignore 文件"""
        print("\n📝 创建 .gitignore...")
        
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache
cache/*.pkl
*.tmp
*.bak

# Model files (optional - uncomment if you don't want to track models)
# models/*.gguf

# Test files
test_*.py
*_test.py
debug_*.py
"""
        
        with open(".gitignore", "w", encoding="utf-8") as f:
            f.write(gitignore_content)
        
        print("✅ .gitignore 已创建")
    
    def run_cleanup(self):
        """运行完整清理"""
        print("🧹 Qwen LLM Platform 项目清理")
        print("=" * 60)
        
        # 确认清理
        response = input("⚠️  确定要清理项目吗？这将删除测试和临时文件 (y/N): ")
        if response.lower() != 'y':
            print("❌ 清理已取消")
            return
        
        # 执行清理
        self.clean_pycache()
        self.clean_temporary_files()
        self.clean_test_files()
        self.clean_duplicate_files()
        self.organize_remaining_files()
        self.create_gitignore()
        
        # 显示总结
        self.show_summary()
        
        print("\n💡 建议:")
        print("1. 检查保留的文件是否正确")
        print("2. 测试核心功能是否正常")
        print("3. 提交清理后的代码到版本控制")

if __name__ == "__main__":
    cleaner = ProjectCleaner()
    cleaner.run_cleanup()
