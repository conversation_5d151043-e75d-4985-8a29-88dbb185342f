#!/usr/bin/env python3
"""
调试路径问题
"""

import os
import sys

def debug_paths():
    print("🔍 路径调试信息")
    print("=" * 50)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python 脚本路径: {__file__}")
    print(f"Python 脚本绝对路径: {os.path.abspath(__file__)}")
    
    # 模拟 qwen_model.py 中的路径计算
    print("\n📁 模拟 qwen_model.py 路径计算:")
    
    # 假设我们在 src/models/qwen_model.py
    qwen_model_file = "src/models/qwen_model.py"
    print(f"qwen_model.py 路径: {qwen_model_file}")
    
    current_file = os.path.abspath(qwen_model_file)
    print(f"current_file: {current_file}")
    
    src_dir = os.path.dirname(os.path.dirname(current_file))
    print(f"src_dir: {src_dir}")
    
    project_root = os.path.dirname(src_dir)
    print(f"project_root: {project_root}")
    
    config_path = os.path.join(project_root, "models/model_config.json")
    print(f"计算的配置路径: {config_path}")
    print(f"配置文件存在: {os.path.exists(config_path)}")
    
    # 检查实际的配置文件
    print("\n📄 实际文件检查:")
    actual_config = "models/model_config.json"
    print(f"实际配置路径: {actual_config}")
    print(f"实际配置文件存在: {os.path.exists(actual_config)}")
    print(f"实际配置绝对路径: {os.path.abspath(actual_config)}")
    
    if os.path.exists(actual_config):
        with open(actual_config, 'r') as f:
            content = f.read()
        print(f"配置文件内容:\n{content}")

if __name__ == "__main__":
    debug_paths()
