#!/usr/bin/env python3
"""
模型调试脚本 - 检查模型文件和配置
"""

import os
import json
import sys

def check_model_status():
    """检查模型状态"""
    print("🔍 模型状态检查")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 检查项目结构
    print("\n📁 项目结构:")
    for item in ["src", "models", "scripts"]:
        if os.path.exists(item):
            print(f"✅ {item}/ 存在")
        else:
            print(f"❌ {item}/ 不存在")
    
    # 检查模型目录
    models_dir = "models"
    print(f"\n📂 模型目录: {models_dir}")
    
    if os.path.exists(models_dir):
        files = os.listdir(models_dir)
        print(f"文件列表: {files}")
        
        for file in files:
            file_path = os.path.join(models_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  📄 {file}: {size:,} bytes ({size/1024/1024:.1f} MB)")
    else:
        print("❌ 模型目录不存在")
        return False
    
    # 检查配置文件
    config_path = "models/model_config.json"
    print(f"\n⚙️  配置文件: {config_path}")
    
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✅ 配置文件有效")
            print("配置内容:")
            for key, value in config.items():
                print(f"  {key}: {value}")
            
            # 检查模型文件路径
            model_path = config.get("model_path", "")
            print(f"\n🎯 模型文件路径: {model_path}")
            
            if os.path.exists(model_path):
                size = os.path.getsize(model_path)
                print(f"✅ 模型文件存在: {size:,} bytes ({size/1024/1024:.1f} MB)")
                return True
            else:
                print(f"❌ 模型文件不存在: {model_path}")
                
                # 尝试相对路径
                relative_path = os.path.join("models", os.path.basename(model_path))
                if os.path.exists(relative_path):
                    size = os.path.getsize(relative_path)
                    print(f"✅ 找到相对路径的模型文件: {relative_path}")
                    print(f"   大小: {size:,} bytes ({size/1024/1024:.1f} MB)")
                    
                    # 更新配置文件
                    config["model_path"] = os.path.abspath(relative_path)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    print("✅ 已更新配置文件中的模型路径")
                    return True
                else:
                    print(f"❌ 相对路径也找不到模型文件: {relative_path}")
                    return False
                    
        except Exception as e:
            print(f"❌ 配置文件读取失败: {e}")
            return False
    else:
        print("❌ 配置文件不存在")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🧪 测试模型加载")
    print("=" * 50)
    
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python 导入成功")
    except ImportError as e:
        print(f"❌ llama-cpp-python 导入失败: {e}")
        return False
    
    # 读取配置
    config_path = "models/model_config.json"
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        model_path = config.get("model_path", "")
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False
        
        print(f"🔄 尝试加载模型: {model_path}")
        
        # 尝试加载模型
        model = Llama(
            model_path=model_path,
            n_ctx=512,  # 使用较小的上下文长度进行测试
            n_threads=4,
            verbose=False
        )
        
        print("✅ 模型加载成功")
        
        # 测试生成
        print("🧪 测试文本生成...")
        result = model("你好", max_tokens=10)
        print(f"✅ 生成测试成功: {result['choices'][0]['text'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Qwen 模型调试工具")
    print("=" * 60)
    
    # 检查模型状态
    model_ok = check_model_status()
    
    if model_ok:
        # 测试模型加载
        loading_ok = test_model_loading()
        
        if loading_ok:
            print("\n🎉 所有检查通过！模型可以正常使用")
            print("\n💡 建议:")
            print("1. 运行 ./quick_start.sh 启动服务")
            print("2. 访问 http://localhost:8000/docs 测试 API")
        else:
            print("\n❌ 模型加载测试失败")
            print("\n💡 建议:")
            print("1. 检查 llama-cpp-python 是否正确安装")
            print("2. 尝试重新下载模型文件")
    else:
        print("\n❌ 模型文件检查失败")
        print("\n💡 建议:")
        print("1. 运行 ./scripts/download_model_alternative.sh 重新下载模型")
        print("2. 检查网络连接")
        print("3. 确保有足够的磁盘空间")

if __name__ == "__main__":
    main()
