# Qwen LLM Platform Docker 镜像
# 支持多架构部署 (x86_64, ARM64)

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY models/ ./models/

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 编译 llama.cpp
RUN git clone https://github.com/ggerganov/llama.cpp.git && \
    cd llama.cpp && \
    make -j$(nproc) && \
    cd ..

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV MODEL_PATH=/app/models
ENV API_HOST=0.0.0.0
ENV API_PORT=8000

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "src/main.py"]
