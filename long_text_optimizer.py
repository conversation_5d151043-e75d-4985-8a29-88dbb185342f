#!/usr/bin/env python3
"""
长文本生成专项优化
"""

import os
import json
import requests
import time

def create_long_text_config():
    """创建长文本专用配置"""
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 创建长文本专用配置...")
    
    # 长文本优化配置
    long_text_config = {
        "model_name": config.get("model_name", "Qwen-1.5-4B-Chat"),
        "model_file": config.get("model_file", "qwen1_5-4b-chat-q4_k_m.gguf"),
        "model_path": config.get("model_path", ""),
        
        # 长文本专用参数
        "context_length": 4096,         # 大幅增加上下文
        "max_tokens": 2048,             # 增加最大输出
        "temperature": 0.8,             # 提高创造性
        "top_p": 0.95,                  # 增加采样多样性
        "repeat_penalty": 1.1,          # 避免重复
        
        # CPU 优化（保持性能）
        "threads": min(8, os.cpu_count() or 4),
        "n_batch": 512,                 # 增加批处理
        "n_predict": -1,                # 不限制预测长度
        
        # 内存优化
        "use_mmap": True,
        "use_mlock": False,
        
        # 长文本生成优化
        "rope_freq_base": 10000,
        "rope_freq_scale": 1.0,
        "mul_mat_q": True,
        
        # 关闭不必要功能
        "gpu_layers": 0,
        "flash_attn": False,
        "logits_all": False,
        "embedding": False,
        
        # 并行设置
        "enable_parallel": True,
        "max_workers": 1,               # 单工作器专注长文本
        "lazy_loading": True
    }
    
    print("📊 配置对比:")
    print(f"  context_length: {config.get('context_length', 'unknown')} → {long_text_config['context_length']}")
    print(f"  max_tokens: {config.get('max_tokens', 'unknown')} → {long_text_config['max_tokens']}")
    print(f"  temperature: {config.get('temperature', 'unknown')} → {long_text_config['temperature']}")
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(long_text_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 长文本配置已保存")
    return True

def update_lightweight_pool():
    """更新轻量级池以支持长文本"""
    pool_file = "src/parallel/lightweight_pool.py"
    
    if not os.path.exists(pool_file):
        print("❌ 轻量级池文件不存在")
        return False
    
    print("🔧 建议手动更新轻量级池:")
    print("1. 增加 n_ctx 到 4096")
    print("2. 移除所有停止词（除了 <|endoftext|>）")
    print("3. 增加 n_batch 到 512")
    
    return True

def test_progressive_length():
    """渐进式测试不同长度"""
    print("\n🧪 渐进式长度测试...")
    
    test_cases = [
        {
            "message": "简单介绍红楼梦",
            "max_tokens": 100,
            "expected": "短回答测试"
        },
        {
            "message": "详细介绍红楼梦的主要人物",
            "max_tokens": 300,
            "expected": "中等长度测试"
        },
        {
            "message": "写一篇关于红楼梦的详细文章，包括人物、情节、主题",
            "max_tokens": 800,
            "expected": "长文本测试"
        },
        {
            "message": "写一篇详细的红楼梦分析文章，包括人物关系、重要章节、文学价值、社会意义等方面，要求内容丰富详实",
            "max_tokens": 1500,
            "expected": "超长文本测试"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['expected']}")
        print(f"请求长度: {test_case['max_tokens']} tokens")
        print(f"提示: {test_case['message'][:50]}...")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8000/chat",
                json={
                    "message": test_case["message"],
                    "max_tokens": test_case["max_tokens"],
                    "temperature": 0.8
                },
                timeout=60  # 增加超时时间
            )
            
            elapsed = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                tokens_used = data.get('tokens_used', 0)
                
                # 计算效率
                efficiency = (tokens_used / test_case['max_tokens']) * 100
                
                result = {
                    "test": test_case['expected'],
                    "requested": test_case['max_tokens'],
                    "actual": tokens_used,
                    "efficiency": efficiency,
                    "time": elapsed,
                    "length": len(response_text),
                    "success": True
                }
                
                results.append(result)
                
                print(f"✅ 成功")
                print(f"   实际 tokens: {tokens_used}/{test_case['max_tokens']} ({efficiency:.1f}%)")
                print(f"   文本长度: {len(response_text)} 字符")
                print(f"   处理时间: {elapsed:.1f}s")
                print(f"   速度: {tokens_used/elapsed:.1f} tokens/s")
                
                # 显示部分内容
                if len(response_text) > 200:
                    print(f"   内容预览: {response_text[:200]}...")
                else:
                    print(f"   完整内容: {response_text}")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                results.append({
                    "test": test_case['expected'],
                    "success": False,
                    "error": response.status_code
                })
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 错误: {e} (耗时: {elapsed:.1f}s)")
            results.append({
                "test": test_case['expected'],
                "success": False,
                "error": str(e)
            })
        
        time.sleep(3)  # 等待间隔
    
    return results

def analyze_results(results):
    """分析测试结果"""
    print("\n📊 长文本生成能力分析")
    print("=" * 60)
    
    successful_results = [r for r in results if r.get('success')]
    
    if not successful_results:
        print("❌ 没有成功的测试结果")
        return
    
    print("成功的测试:")
    for result in successful_results:
        efficiency = result.get('efficiency', 0)
        print(f"  {result['test']}:")
        print(f"    Token 效率: {efficiency:.1f}%")
        print(f"    处理速度: {result.get('actual', 0)/result.get('time', 1):.1f} tokens/s")
        print(f"    文本长度: {result.get('length', 0)} 字符")
    
    # 找出最佳长度
    max_tokens = max(r.get('actual', 0) for r in successful_results)
    best_result = max(successful_results, key=lambda r: r.get('actual', 0))
    
    print(f"\n🎯 当前最佳表现:")
    print(f"  最大生成: {max_tokens} tokens")
    print(f"  最佳测试: {best_result['test']}")
    print(f"  效率: {best_result.get('efficiency', 0):.1f}%")
    
    # 建议
    print(f"\n💡 优化建议:")
    if max_tokens < 500:
        print("  - 当前适合短到中等长度文本")
        print("  - 建议增加 context_length 和 max_tokens")
        print("  - 检查停止词设置")
    elif max_tokens < 1000:
        print("  - 当前适合中等到长文本")
        print("  - 可以尝试更大的 max_tokens")
        print("  - 优化提示词以获得更长响应")
    else:
        print("  - 长文本生成能力良好")
        print("  - 可以尝试更复杂的长文本任务")

def create_long_text_prompt_templates():
    """创建长文本提示模板"""
    templates = {
        "detailed_article": "请写一篇详细的文章关于{topic}。文章应该包含以下部分：1. 引言 2. 主要内容（分多个段落详细阐述）3. 具体例子和分析 4. 总结。请确保内容丰富、结构清晰，字数不少于1000字。",
        
        "comprehensive_analysis": "请对{topic}进行全面深入的分析。分析应包括：1. 背景介绍 2. 主要特点和组成部分 3. 重要意义和影响 4. 具体案例分析 5. 未来发展趋势 6. 结论。请详细展开每个部分，提供充分的信息和见解。",
        
        "story_retelling": "请详细复述{story}的故事内容。包括：1. 故事背景和设定 2. 主要人物介绍 3. 情节发展（按时间顺序详细描述）4. 重要场景和对话 5. 主题思想和意义。请确保内容完整、生动有趣。",
        
        "tutorial_guide": "请写一份关于{topic}的详细教程指南。指南应包括：1. 基础概念介绍 2. 准备工作 3. 详细步骤说明（分步骤详细解释）4. 注意事项和常见问题 5. 进阶技巧 6. 总结和建议。请确保内容实用、易懂。"
    }
    
    print("\n📝 长文本提示模板:")
    for name, template in templates.items():
        print(f"\n{name}:")
        print(f"  {template}")
    
    return templates

def main():
    """主函数"""
    print("🚀 长文本生成专项优化")
    print("=" * 60)
    
    # 1. 创建长文本配置
    if create_long_text_config():
        print("\n✅ 长文本配置创建完成")
    
    # 2. 更新轻量级池建议
    update_lightweight_pool()
    
    # 3. 创建提示模板
    create_long_text_prompt_templates()
    
    print("\n🔄 下一步操作:")
    print("1. 重启服务以应用新配置")
    print("2. 运行渐进式测试: python long_text_optimizer.py test")
    print("3. 根据测试结果进一步调整")
    
    # 4. 可选测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        print("\n开始渐进式测试...")
        results = test_progressive_length()
        analyze_results(results)

if __name__ == "__main__":
    main()
