#!/usr/bin/env python3
"""
翻译功能专项测试
"""

import requests
import time
import json

def test_translation():
    """测试翻译功能"""
    
    api_url = "http://localhost:8000"
    
    # 测试用例
    test_cases = [
        # 英文 -> 中文
        {
            "text": "Hello, how are you today?",
            "target_language": "zh",
            "description": "英文问候语"
        },
        {
            "text": "Artificial Intelligence is changing the world.",
            "target_language": "zh", 
            "description": "英文技术句子"
        },
        {
            "text": "I love programming and machine learning.",
            "target_language": "zh",
            "description": "英文兴趣表达"
        },
        
        # 中文 -> 英文
        {
            "text": "你好，今天天气很好。",
            "target_language": "en",
            "description": "中文日常对话"
        },
        {
            "text": "人工智能正在快速发展，改变着我们的生活方式。",
            "target_language": "en",
            "description": "中文技术描述"
        },
        {
            "text": "我喜欢学习新技术，特别是机器学习和深度学习。",
            "target_language": "en",
            "description": "中文学习表达"
        },
        
        # 其他语言 -> 中文
        {
            "text": "Bonjour, comment allez-vous?",
            "target_language": "zh",
            "description": "法语问候"
        },
        {
            "text": "Hola, ¿cómo estás?",
            "target_language": "zh",
            "description": "西班牙语问候"
        },
        {
            "text": "こんにちは、元気ですか？",
            "target_language": "zh",
            "description": "日语问候"
        },
        
        # 长文本翻译
        {
            "text": "Machine learning is a subset of artificial intelligence that focuses on algorithms and statistical models that enable computers to improve their performance on a specific task through experience.",
            "target_language": "zh",
            "description": "英文长句技术定义"
        }
    ]
    
    print("🌐 翻译功能测试")
    print("=" * 60)
    
    success_count = 0
    total_time = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['description']}")
        print(f"原文: {test_case['text']}")
        print(f"目标语言: {test_case['target_language']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{api_url}/translate",
                json={
                    "text": test_case["text"],
                    "target_language": test_case["target_language"]
                },
                timeout=30
            )
            
            elapsed = time.time() - start_time
            total_time += elapsed
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 翻译结果: {result['translated_text']}")
                print(f"⏱️  耗时: {elapsed:.2f}s")
                
                # 检查翻译质量
                if len(result['translated_text'].strip()) > 0:
                    success_count += 1
                    print("✅ 翻译成功")
                else:
                    print("⚠️  翻译结果为空")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 测试失败: {e}")
            print(f"⏱️  耗时: {elapsed:.2f}s")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print(f"总测试数: {len(test_cases)}")
    print(f"成功数: {success_count}")
    print(f"成功率: {success_count/len(test_cases)*100:.1f}%")
    print(f"平均耗时: {total_time/len(test_cases):.2f}s")
    
    if success_count == len(test_cases):
        print("🎉 所有翻译测试通过！")
    elif success_count >= len(test_cases) * 0.8:
        print("✅ 翻译功能基本正常")
    else:
        print("⚠️  翻译功能需要优化")

def test_translation_quality():
    """测试翻译质量"""
    
    print("\n🎯 翻译质量测试")
    print("=" * 40)
    
    # 质量测试用例（有标准答案的）
    quality_tests = [
        {
            "text": "Good morning",
            "target_language": "zh",
            "expected_keywords": ["早上", "上午", "早晨", "好"]
        },
        {
            "text": "Thank you very much",
            "target_language": "zh", 
            "expected_keywords": ["谢谢", "感谢", "非常"]
        },
        {
            "text": "你好世界",
            "target_language": "en",
            "expected_keywords": ["hello", "world", "hi"]
        }
    ]
    
    api_url = "http://localhost:8000"
    
    for i, test in enumerate(quality_tests, 1):
        print(f"\n质量测试 {i}:")
        print(f"原文: {test['text']}")
        
        try:
            response = requests.post(
                f"{api_url}/translate",
                json={
                    "text": test["text"],
                    "target_language": test["target_language"]
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                translation = result["translated_text"].lower()
                print(f"翻译: {result['translated_text']}")
                
                # 检查关键词
                found_keywords = []
                for keyword in test["expected_keywords"]:
                    if keyword.lower() in translation:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"✅ 质量检查通过，包含关键词: {found_keywords}")
                else:
                    print(f"⚠️  质量检查警告，未找到预期关键词: {test['expected_keywords']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")

def interactive_translation():
    """交互式翻译测试"""
    
    print("\n💬 交互式翻译测试")
    print("输入 'quit' 退出")
    print("=" * 40)
    
    api_url = "http://localhost:8000"
    
    while True:
        try:
            text = input("\n请输入要翻译的文本: ").strip()
            if text.lower() == 'quit':
                break
            
            if not text:
                continue
            
            target_lang = input("目标语言 (zh/en/fr/es/ja): ").strip() or "zh"
            
            response = requests.post(
                f"{api_url}/translate",
                json={
                    "text": text,
                    "target_language": target_lang
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"🌐 翻译结果: {result['translated_text']}")
            else:
                print(f"❌ 翻译失败: {response.status_code}")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n👋 翻译测试结束")

if __name__ == "__main__":
    print("🚀 Qwen LLM Platform 翻译功能测试")
    print("=" * 60)
    
    try:
        # 1. 基础功能测试
        test_translation()
        
        # 2. 质量测试
        test_translation_quality()
        
        # 3. 交互式测试（可选）
        choice = input("\n是否进行交互式翻译测试？(y/n): ").strip().lower()
        if choice == 'y':
            interactive_translation()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        print("请确保 Qwen LLM Platform 服务正在运行")
