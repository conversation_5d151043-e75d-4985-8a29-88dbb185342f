#!/usr/bin/env python3
"""
立即修复服务
"""

import os
import json

def fix_config_immediately():
    """立即修复配置"""
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    # 读取当前配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("🔧 立即修复配置...")
    
    # 备份当前配置
    backup_path = config_path + ".backup"
    with open(backup_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    # 创建稳定的配置
    stable_config = {
        "model_name": config.get("model_name", "Qwen-1.5-4B-Chat"),
        "model_file": config.get("model_file", "qwen1_5-4b-chat-q4_k_m.gguf"),
        "model_path": config.get("model_path", ""),
        
        # 稳定的参数
        "context_length": 1024,        # 适中的上下文
        "max_tokens": 512,             # 适中的输出
        "temperature": 0.7,            # 适中的创造性
        "top_p": 0.9,
        "repeat_penalty": 1.05,
        
        # 稳定的系统参数
        "threads": 4,
        "n_batch": 256,
        "use_mmap": True,
        "use_mlock": False,
        
        # 基础设置
        "gpu_layers": 0,
        "enable_parallel": True,
        "max_workers": 1,
        "lazy_loading": True
    }
    
    # 保存稳定配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(stable_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 稳定配置已保存")
    print(f"   备份原配置: {backup_path}")
    print(f"   context_length: {stable_config['context_length']}")
    print(f"   max_tokens: {stable_config['max_tokens']}")
    
    return True

def test_after_fix():
    """修复后测试"""
    import requests
    import time
    
    print("\n🧪 修复后测试...")
    
    # 等待服务重启
    print("等待服务重启...")
    time.sleep(5)
    
    # 测试健康检查
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"⚠️  健康检查异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return
    
    # 测试简单请求
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": "hello",
                "max_tokens": 20,
                "temperature": 0.7
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 简单请求成功")
            print(f"   响应: {data.get('response', '')}")
            print(f"   Token: {data.get('tokens_used', 0)}")
        else:
            print(f"❌ 简单请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"❌ 简单请求异常: {e}")
        return
    
    # 测试中等长度请求
    try:
        response = requests.post(
            "http://localhost:8000/chat",
            json={
                "message": "请简单介绍一下红楼梦的主要内容",
                "max_tokens": 200,
                "temperature": 0.7
            },
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 中等请求成功")
            print(f"   Token: {data.get('tokens_used', 0)}")
            print(f"   响应长度: {len(data.get('response', ''))} 字符")
            print(f"   响应预览: {data.get('response', '')[:100]}...")
        else:
            print(f"❌ 中等请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 中等请求异常: {e}")

if __name__ == "__main__":
    print("🚀 立即修复 Qwen LLM Platform")
    print("=" * 50)
    
    if fix_config_immediately():
        print("\n✅ 配置修复完成")
        print("\n🔄 请重启服务:")
        print("   1. 停止当前服务 (Ctrl+C)")
        print("   2. 重新启动: python fast_start.py")
        print("   3. 运行测试: python fix_service_now.py test")
        
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == "test":
            test_after_fix()
    else:
        print("❌ 配置修复失败")
