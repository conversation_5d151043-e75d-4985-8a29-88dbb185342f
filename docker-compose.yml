version: '3.8'

services:
  qwen-llm:
    build: .
    container_name: qwen-llm-platform
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - LOG_LEVEL=INFO
      - MODEL_PATH=/app/models
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 16G
        reservations:
          memory: 8G

  # 可选：添加 Redis 用于缓存
  redis:
    image: redis:7-alpine
    container_name: qwen-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: qwen-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - qwen-llm
    restart: unless-stopped

volumes:
  redis_data:
