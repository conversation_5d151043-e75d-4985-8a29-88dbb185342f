#!/bin/bash

# Qwen LLM Platform 完整功能测试脚本

set -e

echo "🧪 Qwen LLM Platform 完整功能测试"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "src/main.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查虚拟环境
if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ ! -d "venv" ]; then
        echo "❌ 虚拟环境不存在，请先运行: ./scripts/setup_mac.sh"
        exit 1
    fi
    
    echo "🐍 激活虚拟环境..."
    source venv/bin/activate
fi

# 检查模型文件
if [ ! -f "models/model_config.json" ]; then
    echo "❌ 模型配置不存在，请先运行: ./scripts/download_model.sh"
    exit 1
fi

# 检查依赖
echo "📚 检查依赖..."
python -c "import httpx, fastapi, uvicorn" 2>/dev/null || {
    echo "📦 安装缺失的依赖..."
    pip install httpx fastapi uvicorn
}

# 启动服务
echo "🚀 启动 Qwen LLM Platform..."
cd src

# 后台启动服务
python main.py &
SERVER_PID=$!

echo "📝 服务进程 ID: $SERVER_PID"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务是否启动成功
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "❌ 服务启动失败"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 服务启动成功！"

# 返回项目根目录
cd ..

# 运行测试
echo "🧪 开始运行完整功能测试..."
python scripts/test_local.py

# 等待测试完成
sleep 2

# 停止服务
echo "🛑 停止服务..."
kill $SERVER_PID 2>/dev/null || true

# 等待进程结束
sleep 3

echo ""
echo "🎉 测试完成！"
echo ""

# 显示测试结果摘要
if [ -f "test_results.json" ]; then
    echo "📊 测试结果摘要："
    echo "=================="
    
    # 使用 Python 解析 JSON 并显示摘要
    python3 << 'EOF'
import json
import sys

try:
    with open('test_results.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    print("✅ 成功的测试:")
    success_count = 0
    total_count = 0
    
    for test_name, result in results.items():
        total_count += 1
        if not result.get('error'):
            success_count += 1
            print(f"  ✓ {test_name}")
        else:
            print(f"  ✗ {test_name}: {result.get('error', 'Unknown error')}")
    
    print(f"\n📈 总体通过率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    # 显示性能数据
    if 'performance' in results and not results['performance'].get('error'):
        perf = results['performance']
        print(f"\n⚡ 性能测试:")
        print(f"  平均响应时间: {perf.get('avg_response_time', 0):.2f}s")
        print(f"  成功请求: {perf.get('successful', 0)}/{perf.get('total_requests', 0)}")

except Exception as e:
    print(f"❌ 无法解析测试结果: {e}")
    sys.exit(1)
EOF

    echo ""
    echo "📄 详细结果已保存到: test_results.json"
else
    echo "❌ 未找到测试结果文件"
fi

echo ""
echo "🔗 有用的链接:"
echo "  📖 API 文档: http://localhost:8000/docs"
echo "  🔍 健康检查: http://localhost:8000/health"
echo "  📋 项目文档: docs/DEPLOYMENT_GUIDE.md"
echo ""
echo "💡 提示: 使用 ./start.sh 可以交互式启动服务"
