#!/usr/bin/env python3
"""
高级模型优化 - 进一步提升性能
"""

import os
import json
import multiprocessing
import psutil

def get_system_info():
    """获取系统信息"""
    cpu_count = multiprocessing.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    return {
        "cpu_cores": cpu_count,
        "memory_gb": int(memory_gb),
        "performance_cores": cpu_count // 2 if cpu_count > 4 else cpu_count
    }

def ultra_optimize_config():
    """超级优化配置"""
    
    config_path = "models/model_config.json"
    
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return False
    
    # 获取系统信息
    sys_info = get_system_info()
    print(f"🖥️  系统信息: {sys_info['cpu_cores']}核 CPU, {sys_info['memory_gb']}GB 内存")
    
    # 读取当前配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("\n🔧 当前配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    # 超级优化配置
    optimized_config = {
        "model_name": config.get("model_name", "Qwen-1.5-4B-Chat"),
        "model_file": config.get("model_file", "qwen1_5-4b-chat-q4_k_m.gguf"),
        "model_path": config.get("model_path", ""),
        
        # 极致性能优化
        "context_length": 512,           # 进一步减少上下文
        "max_tokens": 128,               # 减少最大输出
        "temperature": 0.1,              # 最低随机性
        "top_p": 0.7,                    # 减少采样范围
        "repeat_penalty": 1.01,          # 最小重复惩罚
        
        # CPU 优化
        "threads": sys_info["performance_cores"],  # 使用性能核心
        "n_batch": 256,                  # 减少批处理大小
        "n_predict": 64,                 # 减少预测长度
        
        # 内存优化
        "use_mmap": True,                # 启用内存映射
        "use_mlock": True,               # 锁定内存
        "low_vram": True,                # 低显存模式
        
        # 推理优化
        "rope_freq_base": 10000,         # RoPE 频率基数
        "rope_freq_scale": 1.0,          # RoPE 频率缩放
        "mul_mat_q": True,               # 量化矩阵乘法
        
        # GPU 设置（CPU 模式）
        "gpu_layers": 0,
        "main_gpu": 0,
        
        # 新增优化参数
        "flash_attn": False,             # 关闭 Flash Attention（CPU 模式）
        "logits_all": False,             # 不计算所有 logits
        "embedding": False,              # 不使用嵌入模式
        "numa": False                    # 关闭 NUMA
    }
    
    print("\n⚡ 超级优化后配置:")
    for key, value in optimized_config.items():
        if value != config.get(key):
            print(f"  {key}: {config.get(key, 'new')} → {value} ✨")
        else:
            print(f"  {key}: {value}")
    
    # 保存优化配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 超级优化配置已保存")
    return True

def optimize_model_loading():
    """优化模型加载代码"""
    
    optimized_model_code = '''
import os
import time
from typing import Optional, Dict, Any
from llama_cpp import Llama

class UltraFastQwenModel:
    """超快速 Qwen 模型"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        self.cache = {}  # 简单缓存
        
    def load_model(self, model_path: str, config: Dict[str, Any]):
        """超快速加载模型"""
        print(f"⚡ 超快速加载模型: {os.path.basename(model_path)}")
        
        start_time = time.time()
        
        self.model = Llama(
            model_path=model_path,
            
            # 核心性能参数
            n_ctx=config.get("context_length", 512),
            n_threads=config.get("threads", 8),
            n_batch=config.get("n_batch", 256),
            
            # 内存优化
            use_mmap=config.get("use_mmap", True),
            use_mlock=config.get("use_mlock", True),
            
            # 推理优化
            rope_freq_base=config.get("rope_freq_base", 10000),
            rope_freq_scale=config.get("rope_freq_scale", 1.0),
            mul_mat_q=config.get("mul_mat_q", True),
            
            # 关闭不必要的功能
            logits_all=config.get("logits_all", False),
            embedding=config.get("embedding", False),
            
            # 静默模式
            verbose=False
        )
        
        load_time = time.time() - start_time
        self.is_loaded = True
        
        print(f"✅ 模型加载完成，耗时: {load_time:.2f}s")
        
        # 预热模型
        self._warmup()
        
    def _warmup(self):
        """预热模型"""
        print("🔥 预热模型...")
        
        warmup_prompts = ["hi", "ok", "yes"]
        
        for prompt in warmup_prompts:
            try:
                self.model(prompt, max_tokens=1, temperature=0.1)
            except:
                pass
        
        print("✅ 模型预热完成")
        
    def generate_ultra_fast(self, prompt: str, max_tokens: int = 64) -> Dict[str, Any]:
        """超快速生成"""
        if not self.is_loaded:
            return {"error": "模型未加载"}
        
        # 简单缓存检查
        cache_key = f"{prompt[:50]}_{max_tokens}"
        if cache_key in self.cache:
            print("💾 使用缓存结果")
            return self.cache[cache_key]
        
        start_time = time.time()
        
        # 优化的生成参数
        result = self.model(
            prompt,
            max_tokens=min(max_tokens, 128),  # 限制最大长度
            temperature=0.1,                  # 最低随机性
            top_p=0.7,                       # 减少采样
            repeat_penalty=1.01,             # 最小重复惩罚
            stop=["\\n\\n", "\\n", "。", ".", "!", "?", "！", "？"],  # 早停
            echo=False                       # 不回显
        )
        
        elapsed = time.time() - start_time
        
        response_data = {
            "text": result["choices"][0]["text"].strip(),
            "tokens_used": result["usage"]["total_tokens"],
            "processing_time": elapsed,
            "model": "Qwen-Ultra-Fast"
        }
        
        # 缓存结果（限制缓存大小）
        if len(self.cache) < 100:
            self.cache[cache_key] = response_data
        
        print(f"⚡ 生成完成: {elapsed:.2f}s, {result['usage']['total_tokens']} tokens")
        
        return response_data
'''
    
    with open("ultra_fast_model.py", "w", encoding="utf-8") as f:
        f.write(optimized_model_code)
    
    print("✅ 超快速模型代码已创建: ultra_fast_model.py")

def create_performance_monitor():
    """创建性能监控脚本"""
    
    monitor_code = '''#!/usr/bin/env python3
"""
性能监控脚本
"""

import time
import psutil
import requests
import threading
from datetime import datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.stats = []
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        
        def monitor_loop():
            while self.monitoring:
                stats = {
                    "timestamp": datetime.now().isoformat(),
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_percent": psutil.virtual_memory().percent,
                    "memory_used_gb": psutil.virtual_memory().used / (1024**3)
                }
                
                self.stats.append(stats)
                
                if len(self.stats) > 60:  # 保留最近60个数据点
                    self.stats.pop(0)
                
                time.sleep(1)
        
        thread = threading.Thread(target=monitor_loop, daemon=True)
        thread.start()
        print("📊 性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        print("📊 性能监控已停止")
    
    def get_stats(self):
        """获取统计信息"""
        if not self.stats:
            return {"message": "暂无数据"}
        
        cpu_avg = sum(s["cpu_percent"] for s in self.stats) / len(self.stats)
        memory_avg = sum(s["memory_percent"] for s in self.stats) / len(self.stats)
        
        return {
            "average_cpu": f"{cpu_avg:.1f}%",
            "average_memory": f"{memory_avg:.1f}%",
            "current_memory_gb": f"{self.stats[-1]['memory_used_gb']:.1f}GB",
            "data_points": len(self.stats)
        }
    
    def benchmark_api(self, api_url="http://localhost:8000"):
        """API 性能基准测试"""
        
        test_cases = [
            {"message": "hi", "max_tokens": 5},
            {"message": "hello", "max_tokens": 10},
            {"message": "what is AI", "max_tokens": 20},
        ]
        
        print("🚀 开始 API 基准测试...")
        
        results = []
        
        for i, test_case in enumerate(test_cases):
            print(f"测试 {i+1}/3: {test_case['message']}")
            
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{api_url}/chat",
                    json=test_case,
                    timeout=30
                )
                
                elapsed = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    results.append({
                        "test": test_case["message"],
                        "response_time": elapsed,
                        "tokens": data.get("tokens_used", 0),
                        "success": True
                    })
                    print(f"✅ 响应时间: {elapsed:.2f}s")
                else:
                    results.append({
                        "test": test_case["message"],
                        "response_time": elapsed,
                        "success": False
                    })
                    print(f"❌ 失败: {response.status_code}")
                    
            except Exception as e:
                elapsed = time.time() - start_time
                results.append({
                    "test": test_case["message"],
                    "response_time": elapsed,
                    "error": str(e),
                    "success": False
                })
                print(f"❌ 错误: {e}")
        
        # 计算平均性能
        successful = [r for r in results if r.get("success")]
        if successful:
            avg_time = sum(r["response_time"] for r in successful) / len(successful)
            avg_tokens = sum(r.get("tokens", 0) for r in successful) / len(successful)
            
            print(f"\\n📊 基准测试结果:")
            print(f"   成功率: {len(successful)}/{len(results)}")
            print(f"   平均响应时间: {avg_time:.2f}s")
            print(f"   平均 Token 数: {avg_tokens:.0f}")
            
            if avg_time < 2:
                print("🎉 性能优秀！")
            elif avg_time < 4:
                print("✅ 性能良好")
            else:
                print("⚠️  需要进一步优化")

if __name__ == "__main__":
    monitor = PerformanceMonitor()
    
    try:
        monitor.start_monitoring()
        monitor.benchmark_api()
        
        print("\\n📊 系统资源使用情况:")
        stats = monitor.get_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
            
    finally:
        monitor.stop_monitoring()
'''
    
    with open("performance_monitor.py", "w", encoding="utf-8") as f:
        f.write(monitor_code)
    
    print("✅ 性能监控脚本已创建: performance_monitor.py")

def system_optimization_tips():
    """系统优化建议"""
    
    print("\n🔧 系统级优化建议:")
    print("=" * 50)
    
    # 检查系统状态
    cpu_count = multiprocessing.cpu_count()
    memory = psutil.virtual_memory()
    
    print(f"💻 当前系统状态:")
    print(f"   CPU 核心数: {cpu_count}")
    print(f"   总内存: {memory.total / (1024**3):.1f}GB")
    print(f"   可用内存: {memory.available / (1024**3):.1f}GB")
    print(f"   内存使用率: {memory.percent:.1f}%")
    
    print(f"\n⚡ 优化建议:")
    
    if memory.percent > 80:
        print("   🔴 内存使用率过高，建议:")
        print("      - 关闭不必要的应用程序")
        print("      - 重启系统释放内存")
        print("      - 考虑使用更小的模型")
    
    if cpu_count >= 8:
        print("   ✅ CPU 核心充足，已优化线程配置")
    else:
        print("   ⚠️  CPU 核心较少，建议:")
        print("      - 关闭后台进程")
        print("      - 使用更小的批处理大小")
    
    print(f"\n🚀 macOS 特定优化:")
    print("   - 在活动监视器中关闭不必要的进程")
    print("   - 设置节能模式为'高性能'")
    print("   - 确保 SSD 有足够空间")
    print("   - 定期重启释放内存碎片")

if __name__ == "__main__":
    print("⚡ Qwen LLM Platform 高级优化")
    print("=" * 60)
    
    # 1. 超级优化配置
    if ultra_optimize_config():
        print("\n✅ 配置超级优化完成")
    
    # 2. 创建优化模型代码
    optimize_model_loading()
    
    # 3. 创建性能监控
    create_performance_monitor()
    
    # 4. 系统优化建议
    system_optimization_tips()
    
    print("\n🚀 高级优化完成！下一步:")
    print("1. 重启服务: ./start_from_root.py")
    print("2. 运行性能监控: python performance_monitor.py")
    print("3. 测试翻译性能: python translation_test.py")
    print("4. 对比优化前后的性能差异")
    
    print("\n🎯 预期优化效果:")
    print("   - 响应时间: 减少 30-50%")
    print("   - 内存使用: 减少 20-30%")
    print("   - CPU 使用: 更高效的多核利用")
    print("   - 启动时间: 减少 40-60%")
