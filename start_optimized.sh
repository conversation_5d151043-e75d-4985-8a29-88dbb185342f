#!/bin/bash

# 启动优化版 Qwen LLM Platform

set -e

echo "🚀 启动优化版 Qwen LLM Platform"
echo "================================="

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行: ./scripts/setup_mac.sh"
    exit 1
fi

echo "🐍 激活虚拟环境..."
source venv/bin/activate

# 检查模型文件
if [ ! -f "models/model_config.json" ]; then
    echo "❌ 模型配置不存在，请先下载模型"
    exit 1
fi

# 创建缓存目录
mkdir -p cache

# 安装优化依赖
echo "📦 检查优化依赖..."
pip install psutil --quiet

# 检查模型文件大小
MODEL_FILE=$(jq -r '.model_file' models/model_config.json 2>/dev/null || echo "")
if [ -n "$MODEL_FILE" ] && [ -f "models/$MODEL_FILE" ]; then
    MODEL_SIZE=$(stat -f%z "models/$MODEL_FILE" 2>/dev/null || echo "0")
    if [ "$MODEL_SIZE" -lt 1000000 ]; then
        echo "❌ 模型文件损坏，请重新下载"
        exit 1
    fi
    echo "✅ 模型文件检查通过 ($(du -h models/$MODEL_FILE | cut -f1))"
else
    echo "❌ 模型文件不存在"
    exit 1
fi

echo ""
echo "🎯 优化功能状态:"
echo "  ✅ 智能缓存系统"
echo "  ✅ 模型并行处理"
echo "  ✅ 性能监控"
echo "  ✅ 自动优化配置"

echo ""
echo "🚀 启动服务..."
echo "📖 API 文档: http://localhost:8000/docs"
echo "🔍 健康检查: http://localhost:8000/health"
echo "📊 性能统计: http://localhost:8000/stats"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 设置 Python 路径并启动
PYTHONPATH=src python src/main.py
