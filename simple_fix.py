#!/usr/bin/env python3
"""
简单修复：直接使用绝对路径更新配置文件
"""

import os
import json

def fix_config():
    """修复配置文件路径"""
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 配置文件路径
    config_path = os.path.join(project_root, "models", "model_config.json")
    model_path = os.path.join(project_root, "models", "qwen1_5-4b-chat-q4_k_m.gguf")
    
    print(f"项目根目录: {project_root}")
    print(f"配置文件路径: {config_path}")
    print(f"模型文件路径: {model_path}")
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    # 创建新的配置
    config = {
        "model_name": "Qwen-1.5-4B-Chat",
        "model_file": "qwen1_5-4b-chat-q4_k_m.gguf",
        "model_path": model_path,  # 使用绝对路径
        "context_length": 32768,
        "max_tokens": 2048,
        "temperature": 0.7,
        "top_p": 0.8,
        "repeat_penalty": 1.1,
        "threads": 10,
        "gpu_layers": 0
    }
    
    # 保存配置文件
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件已更新: {config_path}")
        print(f"✅ 模型路径: {model_path}")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 修复配置文件路径")
    print("=" * 40)
    
    if fix_config():
        print("\n🎉 修复完成！现在可以启动服务了")
        print("运行: ./start_from_root.py")
    else:
        print("\n❌ 修复失败")
