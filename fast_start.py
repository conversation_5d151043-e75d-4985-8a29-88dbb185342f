#!/usr/bin/env python3
"""
快速启动脚本 - 最小化启动时间
"""

import os
import sys

# 添加 src 到路径
sys.path.insert(0, 'src')

def fast_start():
    """快速启动"""
    print("⚡ Qwen LLM Platform - 快速启动模式")
    print("=" * 50)
    
    # 检查基本环境
    if not os.path.exists("venv"):
        print("❌ 虚拟环境不存在")
        return
    
    if not os.path.exists("models/model_config.json"):
        print("❌ 模型配置不存在")
        return
    
    print("🚀 启动服务...")
    print("📖 API 文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("")
    
    # 设置环境变量
    os.environ["QWEN_FAST_MODE"] = "1"
    os.environ["PYTHONPATH"] = "src"
    
    # 启动服务
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # 关闭热重载
        log_level="warning"  # 减少日志
    )

if __name__ == "__main__":
    fast_start()
