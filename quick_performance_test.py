#!/usr/bin/env python3
"""
快速性能测试 - 对比优化前后的性能
"""

import requests
import time
import json

def quick_test():
    """快速性能测试"""
    
    api_url = "http://localhost:8000"
    
    # 快速测试用例
    test_cases = [
        {"message": "hi", "max_tokens": 5},
        {"message": "hello world", "max_tokens": 10},
        {"message": "translate: hello", "max_tokens": 20},
        {"message": "what is AI", "max_tokens": 30}
    ]
    
    print("⚡ 快速性能测试")
    print("=" * 40)
    
    total_time = 0
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['message']}")
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{api_url}/chat",
                json=test_case,
                timeout=20
            )
            
            elapsed = time.time() - start_time
            total_time += elapsed
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应时间: {elapsed:.2f}s")
                print(f"📝 回复: {data['response'][:30]}...")
                success_count += 1
            else:
                print(f"❌ 失败: {response.status_code}")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ 错误: {e} ({elapsed:.2f}s)")
    
    if success_count > 0:
        avg_time = total_time / success_count
        print(f"\n📊 测试结果:")
        print(f"   平均响应时间: {avg_time:.2f}s")
        
        if avg_time < 1.5:
            print("🚀 性能极佳！")
        elif avg_time < 3:
            print("✅ 性能优秀")
        elif avg_time < 5:
            print("👍 性能良好")
        else:
            print("⚠️  需要进一步优化")

if __name__ == "__main__":
    quick_test()
