
import os
import time
from typing import Optional, Dict, Any
from llama_cpp import Llama

class FastQwenModel:
    """快速 Qwen 模型包装器"""
    
    def __init__(self):
        self.model = None
        self.is_loaded = False
        
    def load_model(self, model_path: str):
        """加载模型（优化参数）"""
        print(f"🚀 快速加载模型: {model_path}")
        
        self.model = Llama(
            model_path=model_path,
            n_ctx=1024,          # 小上下文
            n_threads=os.cpu_count(),  # 最大线程
            n_batch=512,         # 批处理
            verbose=False,       # 关闭详细输出
            use_mmap=True,       # 使用内存映射
            use_mlock=True,      # 锁定内存
            n_gpu_layers=0,      # CPU 推理
        )
        
        self.is_loaded = True
        print("✅ 快速模型加载完成")
        
    def generate_fast(self, prompt: str, max_tokens: int = 128) -> str:
        """快速生成"""
        if not self.is_loaded:
            return "模型未加载"
            
        start_time = time.time()
        
        result = self.model(
            prompt,
            max_tokens=max_tokens,
            temperature=0.1,     # 低随机性
            top_p=0.8,
            repeat_penalty=1.02,
            stop=["\n\n", "<|endoftext|>", "<|im_end|>"],  # 早停
            echo=False           # 不回显输入
        )
        
        elapsed = time.time() - start_time
        response = result["choices"][0]["text"].strip()
        
        print(f"⚡ 生成耗时: {elapsed:.2f}s, Token数: {result['usage']['total_tokens']}")
        
        return response
