#!/usr/bin/env python3
"""
实际应用集成示例
展示如何将 Qwen LLM Platform 集成到现有系统中
"""

import requests
import json
from typing import Dict, Any, List

class QwenIntegrator:
    """Qwen 平台集成器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
    
    def analyze_business_document(self, document_text: str, doc_type: str) -> Dict[str, Any]:
        """分析业务文档"""
        
        response = requests.post(
            f"{self.api_base_url}/api/v1/tasks/oa/approval",
            json={
                "document_content": document_text,
                "approval_type": doc_type
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": "分析失败"}
    
    def process_meeting_notes(self, meeting_text: str, participants: List[str]) -> Dict[str, Any]:
        """处理会议记录"""
        
        response = requests.post(
            f"{self.api_base_url}/api/v1/tasks/meeting/summarize",
            json={
                "transcript": meeting_text,
                "meeting_type": "business",
                "participants": participants
            }
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": "处理失败"}
    
    def smart_email_handler(self, email_subject: str, email_content: str, sender: str) -> Dict[str, Any]:
        """智能邮件处理"""
        
        # 1. 分类邮件
        classify_response = requests.post(
            f"{self.api_base_url}/api/v1/tasks/email/classify",
            json={
                "subject": email_subject,
                "content": email_content,
                "sender": sender
            }
        )
        
        if classify_response.status_code != 200:
            return {"error": "邮件分类失败"}
        
        classification = classify_response.json()
        
        # 2. 如果是重要邮件，生成回复建议
        if classification.get("priority", 0) >= 4:
            reply_response = requests.post(
                f"{self.api_base_url}/api/v1/tasks/email/reply",
                json={
                    "original_email": f"主题: {email_subject}\n内容: {email_content}",
                    "reply_type": "formal"
                }
            )
            
            if reply_response.status_code == 200:
                classification["suggested_reply"] = reply_response.json()
        
        return classification
    
    def educational_assistant(self, question: str, subject: str) -> str:
        """教育助手"""
        
        response = requests.post(
            f"{self.api_base_url}/chat",
            json={
                "message": f"作为{subject}老师，请回答：{question}",
                "max_tokens": 300,
                "temperature": 0.3
            }
        )
        
        if response.status_code == 200:
            return response.json()["response"]
        else:
            return "回答生成失败"

# 实际使用示例
def demo_business_applications():
    """演示业务应用"""
    
    integrator = QwenIntegrator()
    
    print("🏢 业务应用演示")
    print("=" * 50)
    
    # 1. 分析采购申请
    print("\n1. 📋 采购申请分析:")
    purchase_request = """
    申请部门：IT部门
    申请物品：服务器设备
    数量：2台
    预算：50000元
    用途：升级现有系统，提升处理能力
    供应商：Dell官方授权经销商
    """
    
    result = integrator.analyze_business_document(purchase_request, "purchase")
    print(f"分析结果：{result.get('recommendation', '未知')}")
    print(f"风险评分：{result.get('risk_score', 0)}")
    
    # 2. 处理会议记录
    print("\n2. 🎯 会议记录处理:")
    meeting_notes = """
    张总：我们需要讨论Q2的销售策略
    李经理：建议加强线上推广，预算增加30%
    王主管：同意，但需要控制成本
    张总：好的，李经理负责制定详细方案，下周五前提交
    """
    
    meeting_result = integrator.process_meeting_notes(meeting_notes, ["张总", "李经理", "王主管"])
    print(f"会议摘要：{meeting_result.get('summary', '生成失败')[:100]}...")
    
    # 3. 智能邮件处理
    print("\n3. 📧 智能邮件处理:")
    email_result = integrator.smart_email_handler(
        "紧急：客户投诉需要处理",
        "我们的产品出现了质量问题，客户非常不满，需要立即处理",
        "<EMAIL>"
    )
    print(f"邮件类别：{email_result.get('category', '未知')}")
    print(f"优先级：{email_result.get('priority', 0)}")

def demo_educational_applications():
    """演示教育应用"""
    
    integrator = QwenIntegrator()
    
    print("\n📚 教育应用演示")
    print("=" * 50)
    
    # 学科答疑
    subjects = ["数学", "物理", "化学", "历史"]
    questions = [
        "什么是二次函数？",
        "牛顿三大定律是什么？",
        "水的分子式是什么？",
        "秦朝统一了哪些制度？"
    ]
    
    for subject, question in zip(subjects, questions):
        print(f"\n{subject}问题：{question}")
        answer = integrator.educational_assistant(question, subject)
        print(f"AI回答：{answer[:100]}...")

def create_webhook_integration():
    """创建 Webhook 集成示例"""
    
    webhook_code = '''
from flask import Flask, request, jsonify
import requests

app = Flask(__name__)
QWEN_API_URL = "http://localhost:8000"

@app.route("/webhook/email", methods=["POST"])
def handle_email_webhook():
    """处理邮件 Webhook"""
    
    email_data = request.json
    
    # 调用 Qwen API 分析邮件
    response = requests.post(
        f"{QWEN_API_URL}/api/v1/tasks/email/classify",
        json={
            "subject": email_data.get("subject", ""),
            "content": email_data.get("content", ""),
            "sender": email_data.get("sender", "")
        }
    )
    
    if response.status_code == 200:
        classification = response.json()
        
        # 根据分类结果采取行动
        if classification.get("priority", 0) >= 4:
            # 高优先级邮件，发送通知
            send_notification(email_data, classification)
        
        return jsonify({"status": "processed", "classification": classification})
    else:
        return jsonify({"status": "error"}), 500

def send_notification(email_data, classification):
    """发送通知"""
    # 这里可以集成钉钉、企业微信等通知系统
    print(f"高优先级邮件通知：{email_data['subject']}")

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5000)
'''
    
    with open("webhook_integration.py", "w", encoding="utf-8") as f:
        f.write(webhook_code)
    
    print("✅ Webhook 集成示例已创建：webhook_integration.py")

def main():
    """主函数"""
    
    print("🚀 Qwen LLM Platform 实际应用指南")
    print("=" * 60)
    
    try:
        # 演示业务应用
        demo_business_applications()
        
        # 演示教育应用
        demo_educational_applications()
        
        # 创建集成示例
        create_webhook_integration()
        
        print("\n" + "=" * 60)
        print("🎉 应用演示完成！")
        
        print("\n💡 下一步建议：")
        print("1. 根据你的具体需求修改集成代码")
        print("2. 将 API 集成到现有系统中")
        print("3. 设置定时任务处理批量数据")
        print("4. 配置 Webhook 实现实时处理")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误：{e}")
        print("请确保 Qwen LLM Platform 服务正在运行")

if __name__ == "__main__":
    main()
