#!/usr/bin/env python3
"""
安装视频处理依赖
"""

import subprocess
import sys
import platform
import os

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    print(f"✅ Python 版本: {sys.version}")
    return True

def install_whisper():
    """安装 OpenAI Whisper"""
    print("\n🎤 安装 OpenAI Whisper...")
    
    try:
        # 安装 whisper
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "openai-whisper", "--upgrade"
        ])
        
        # 安装额外依赖
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "torch", "torchaudio", "--upgrade"
        ])
        
        print("✅ Whisper 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Whisper 安装失败: {e}")
        return False

def install_ffmpeg():
    """安装 FFmpeg"""
    print("\n🎬 安装 FFmpeg...")
    
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        print("检测到 macOS 系统")
        
        # 检查是否有 Homebrew
        try:
            subprocess.check_call(["brew", "--version"], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
            
            print("使用 Homebrew 安装 FFmpeg...")
            subprocess.check_call(["brew", "install", "ffmpeg"])
            print("✅ FFmpeg 安装成功")
            return True
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ 未找到 Homebrew")
            print("请手动安装 Homebrew 后重试:")
            print("  /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
    
    elif system == "linux":
        print("检测到 Linux 系统")
        
        # 尝试不同的包管理器
        package_managers = [
            (["apt", "update"], ["apt", "install", "-y", "ffmpeg"]),
            (["yum", "update"], ["yum", "install", "-y", "ffmpeg"]),
            (["dnf", "update"], ["dnf", "install", "-y", "ffmpeg"]),
        ]
        
        for update_cmd, install_cmd in package_managers:
            try:
                subprocess.check_call(update_cmd, stdout=subprocess.DEVNULL)
                subprocess.check_call(install_cmd)
                print("✅ FFmpeg 安装成功")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        print("❌ 无法自动安装 FFmpeg")
        print("请手动安装 FFmpeg:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  CentOS/RHEL: sudo yum install ffmpeg")
        return False
    
    elif system == "windows":
        print("检测到 Windows 系统")
        print("❌ 无法自动安装 FFmpeg")
        print("请手动安装 FFmpeg:")
        print("1. 访问 https://ffmpeg.org/download.html")
        print("2. 下载 Windows 版本")
        print("3. 解压并添加到 PATH 环境变量")
        return False
    
    else:
        print(f"❌ 不支持的操作系统: {system}")
        return False

def check_ffmpeg():
    """检查 FFmpeg 是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg 已安装并可用")
            return True
        else:
            print("❌ FFmpeg 不可用")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ FFmpeg 未安装")
        return False

def check_whisper():
    """检查 Whisper 是否可用"""
    try:
        import whisper
        print("✅ Whisper 已安装并可用")
        return True
    except ImportError:
        print("❌ Whisper 未安装")
        return False

def install_additional_deps():
    """安装其他依赖"""
    print("\n📦 安装其他依赖...")
    
    additional_packages = [
        "python-multipart",  # 文件上传支持
        "aiofiles",          # 异步文件操作
    ]
    
    try:
        for package in additional_packages:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                package, "--upgrade"
            ])
        
        print("✅ 其他依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 其他依赖安装失败: {e}")
        return False

def test_video_processing():
    """测试视频处理功能"""
    print("\n🧪 测试视频处理功能...")
    
    try:
        # 测试导入
        from src.integrations.video_processor import VideoProcessor
        
        processor = VideoProcessor()
        deps = processor.check_dependencies()
        processor.cleanup()
        
        print("依赖检查结果:")
        for dep, available in deps.items():
            status = "✅" if available else "❌"
            print(f"  {dep}: {status}")
        
        all_available = all(deps.values())
        
        if all_available:
            print("✅ 视频处理功能完全可用")
        else:
            print("⚠️  部分功能不可用")
        
        return all_available
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("🎥 视频处理依赖安装程序")
    print("=" * 60)
    
    # 检查 Python 版本
    if not check_python_version():
        return
    
    # 检查现有依赖
    print("\n🔍 检查现有依赖...")
    ffmpeg_available = check_ffmpeg()
    whisper_available = check_whisper()
    
    # 安装缺失的依赖
    if not ffmpeg_available:
        if not install_ffmpeg():
            print("\n❌ FFmpeg 安装失败，视频处理功能将不可用")
    
    if not whisper_available:
        if not install_whisper():
            print("\n❌ Whisper 安装失败，语音识别功能将不可用")
    
    # 安装其他依赖
    install_additional_deps()
    
    # 最终测试
    print("\n" + "=" * 60)
    success = test_video_processing()
    
    if success:
        print("\n🎉 视频处理功能安装完成！")
        print("\n💡 使用方法:")
        print("1. 重启服务: python fast_start.py")
        print("2. 上传视频进行处理")
        print("3. 支持的格式: MP4, AVI, MOV, MKV, WebM, FLV, WMV")
    else:
        print("\n⚠️  安装未完全成功，请检查错误信息")
        print("\n🔧 手动安装指南:")
        print("1. FFmpeg: https://ffmpeg.org/download.html")
        print("2. Whisper: pip install openai-whisper")

if __name__ == "__main__":
    main()
