#!/usr/bin/env python3
"""
测试字幕翻译功能
"""

import requests
import json

def test_srt_translation():
    """测试SRT字幕翻译"""
    print("🎬 测试SRT字幕翻译")
    print("=" * 60)
    
    # 示例SRT字幕内容
    srt_content = """1
00:00:01,000 --> 00:00:04,000
Hello, welcome to our tutorial.

2
00:00:05,000 --> 00:00:08,000
Today we will learn about machine learning.

3
00:00:09,000 --> 00:00:12,000
Machine learning is a subset of artificial intelligence.

4
00:00:13,000 --> 00:00:16,000
It enables computers to learn without being explicitly programmed.

5
00:00:17,000 --> 00:00:20,000
Let's start with the basics."""
    
    try:
        response = requests.post(
            "http://localhost:8000/translate_subtitle",
            json={
                "subtitle_content": srt_content,
                "subtitle_format": "srt",
                "target_language": "zh",
                "source_language": "en",
                "bilingual": False
            },
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ SRT翻译成功")
            print(f"   原字幕数量: {data.get('original_count', 0)}")
            print(f"   翻译字幕数量: {data.get('translated_count', 0)}")
            print(f"   目标语言: {data.get('target_language', '')}")
            
            print("\n📝 翻译后的字幕:")
            print("=" * 60)
            print(data.get('translated_srt', ''))
            print("=" * 60)
            
        else:
            print(f"❌ SRT翻译失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ SRT翻译异常: {e}")

def test_bilingual_subtitles():
    """测试双语字幕"""
    print("\n🌐 测试双语字幕生成")
    print("=" * 60)
    
    srt_content = """1
00:00:01,000 --> 00:00:04,000
Good morning everyone!

2
00:00:05,000 --> 00:00:08,000
How are you doing today?

3
00:00:09,000 --> 00:00:12,000
I hope you're having a great day."""
    
    try:
        response = requests.post(
            "http://localhost:8000/translate_subtitle",
            json={
                "subtitle_content": srt_content,
                "subtitle_format": "srt",
                "target_language": "zh",
                "bilingual": True
            },
            timeout=90
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ 双语字幕生成成功")
            print(f"   字幕数量: {data.get('subtitle_count', 0)}")
            print(f"   目标语言: {data.get('target_language', '')}")
            
            print("\n📝 双语字幕:")
            print("=" * 60)
            print(data.get('bilingual_srt', ''))
            print("=" * 60)
            
        else:
            print(f"❌ 双语字幕生成失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 双语字幕生成异常: {e}")

def test_different_languages():
    """测试不同语言翻译"""
    print("\n🌍 测试多语言翻译")
    print("=" * 60)
    
    srt_content = """1
00:00:01,000 --> 00:00:04,000
Thank you for watching!

2
00:00:05,000 --> 00:00:08,000
Please subscribe to our channel."""
    
    languages = [
        ("zh", "中文"),
        ("ja", "日文"),
        ("ko", "韩文"),
        ("fr", "法文")
    ]
    
    for lang_code, lang_name in languages:
        print(f"\n翻译到{lang_name} ({lang_code}):")
        
        try:
            response = requests.post(
                "http://localhost:8000/translate_subtitle",
                json={
                    "subtitle_content": srt_content,
                    "target_language": lang_code,
                    "bilingual": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                translated_srt = data.get('translated_srt', '')
                
                # 提取翻译后的文本
                lines = translated_srt.split('\n')
                translated_texts = []
                for line in lines:
                    if line and not line.isdigit() and '-->' not in line:
                        translated_texts.append(line)
                
                print(f"✅ {lang_name}翻译:")
                for text in translated_texts:
                    print(f"   {text}")
                    
            else:
                print(f"❌ {lang_name}翻译失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {lang_name}翻译异常: {e}")

def create_sample_srt_file():
    """创建示例SRT文件"""
    print("\n📁 创建示例SRT文件")
    print("=" * 60)
    
    sample_srt = """1
00:00:00,000 --> 00:00:03,000
Welcome to the AI tutorial series.

2
00:00:04,000 --> 00:00:07,000
In this episode, we'll explore neural networks.

3
00:00:08,000 --> 00:00:11,000
Neural networks are inspired by the human brain.

4
00:00:12,000 --> 00:00:15,000
They consist of interconnected nodes called neurons.

5
00:00:16,000 --> 00:00:19,000
Each neuron processes information and passes it forward.

6
00:00:20,000 --> 00:00:23,000
Through training, networks learn to recognize patterns.

7
00:00:24,000 --> 00:00:27,000
This makes them powerful tools for many applications.

8
00:00:28,000 --> 00:00:31,000
Thank you for watching, see you next time!"""
    
    with open("sample_video.srt", "w", encoding="utf-8") as f:
        f.write(sample_srt)
    
    print("✅ 示例SRT文件已创建: sample_video.srt")
    
    return sample_srt

def test_long_subtitle():
    """测试长字幕翻译"""
    print("\n📺 测试长字幕翻译")
    print("=" * 60)
    
    # 创建示例文件
    sample_srt = create_sample_srt_file()
    
    try:
        response = requests.post(
            "http://localhost:8000/translate_subtitle",
            json={
                "subtitle_content": sample_srt,
                "target_language": "zh",
                "bilingual": True
            },
            timeout=180  # 3分钟超时
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ 长字幕翻译成功")
            print(f"   字幕数量: {data.get('subtitle_count', 0)}")
            
            # 保存翻译结果
            bilingual_srt = data.get('bilingual_srt', '')
            with open("sample_video_bilingual.srt", "w", encoding="utf-8") as f:
                f.write(bilingual_srt)
            
            print("✅ 双语字幕已保存: sample_video_bilingual.srt")
            
            # 显示部分结果
            lines = bilingual_srt.split('\n')
            print("\n📝 部分翻译结果:")
            print("-" * 40)
            for i, line in enumerate(lines[:20]):  # 显示前20行
                print(line)
            print("-" * 40)
            
        else:
            print(f"❌ 长字幕翻译失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 长字幕翻译异常: {e}")

def main():
    """主函数"""
    print("🎬 视频字幕翻译功能测试")
    print("=" * 80)
    
    # 检查服务状态
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ Qwen 服务运行正常")
        else:
            print(f"⚠️  Qwen 服务状态异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接 Qwen 服务: {e}")
        print("请确保服务正在运行: python fast_start.py")
        return
    
    # 运行测试
    test_srt_translation()
    test_bilingual_subtitles()
    test_different_languages()
    test_long_subtitle()
    
    print("\n🎉 字幕翻译功能测试完成")
    print("\n💡 使用建议:")
    print("1. 支持SRT和VTT格式字幕")
    print("2. 可生成单语或双语字幕")
    print("3. 支持多种目标语言")
    print("4. 适合教育视频、电影、教程等")
    print("5. 翻译质量针对字幕观看优化")

if __name__ == "__main__":
    main()
